<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Debouncing Test - LLMLog</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .test-results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            margin-top: 15px;
        }
        .log-entry {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
            font-family: monospace;
            font-size: 14px;
        }
        .log-entry:last-child {
            border-bottom: none;
        }
        .timestamp {
            color: #666;
            margin-right: 10px;
        }
        .search-count {
            font-weight: bold;
            color: #007bff;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
        .error {
            color: #dc3545;
        }
        .clear-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .clear-btn:hover {
            background: #5a6268;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>🔍 Search Debouncing Test</h1>
    <p>This test verifies that search debouncing is working correctly with a 300ms delay.</p>

    <div class="test-container">
        <h2>Test Search Input</h2>
        <p>Type in the search box below. Each search request should be debounced by 300ms.</p>
        <input type="search" id="test-search-input" class="search-input" placeholder="Type to test search debouncing...">
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="input-count">0</div>
                <div class="stat-label">Input Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="search-count">0</div>
                <div class="stat-label">Search Requests</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="debounce-ratio">0%</div>
                <div class="stat-label">Debounce Efficiency</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="last-delay">-</div>
                <div class="stat-label">Last Delay (ms)</div>
            </div>
        </div>

        <button class="clear-btn" onclick="clearLogs()">Clear Logs</button>
    </div>

    <div class="test-container">
        <h2>Search Request Log</h2>
        <div class="test-results" id="log-container">
            <div class="log-entry">
                <span class="timestamp">Ready</span>
                <span>Waiting for search input...</span>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>Test Instructions</h2>
        <ol>
            <li><strong>Type rapidly:</strong> Type several characters quickly in the search box</li>
            <li><strong>Observe debouncing:</strong> Search requests should only fire 300ms after you stop typing</li>
            <li><strong>Check efficiency:</strong> The debounce ratio should be high (fewer search requests than input events)</li>
            <li><strong>Verify delay:</strong> The actual delay should be close to 300ms</li>
        </ol>
        
        <h3>Expected Behavior:</h3>
        <ul>
            <li>✅ Search requests are delayed by 300ms after the last keystroke</li>
            <li>✅ Rapid typing should result in fewer search requests than input events</li>
            <li>✅ Each search request should include the current search term</li>
            <li>✅ Previous search requests should be cancelled when new input occurs</li>
        </ul>
    </div>

    <script>
        let inputCount = 0;
        let searchCount = 0;
        let lastInputTime = 0;
        let searchTimeout;

        // Mock the search function to simulate the actual popup behavior
        function handleSearch(searchTerm) {
            const now = Date.now();
            const delay = lastInputTime ? now - lastInputTime : 0;
            
            searchCount++;
            
            // Log the search request
            logEntry(`Search executed: "${searchTerm}"`, 'success');
            logEntry(`Delay: ${delay}ms`, delay >= 290 && delay <= 350 ? 'success' : 'warning');
            
            // Update stats
            updateStats(delay);
        }

        // Debounced search implementation (same as popup.js)
        function setupDebouncedSearch() {
            const searchInput = document.getElementById('test-search-input');
            
            searchInput.addEventListener('input', (e) => {
                inputCount++;
                lastInputTime = Date.now();
                
                // Log input event
                logEntry(`Input event: "${e.target.value}" (${inputCount})`, 'info');
                
                // Clear previous timeout
                clearTimeout(searchTimeout);
                
                // Set new timeout (300ms debounce)
                searchTimeout = setTimeout(() => {
                    const searchTerm = e.target.value.toLowerCase();
                    handleSearch(searchTerm);
                }, 300);
                
                // Update input count display
                updateStats();
            });
        }

        function logEntry(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            
            const logDiv = document.createElement('div');
            logDiv.className = `log-entry ${type}`;
            logDiv.innerHTML = `
                <span class="timestamp">${timestamp}</span>
                <span>${message}</span>
            `;
            
            logContainer.appendChild(logDiv);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStats(lastDelay = null) {
            document.getElementById('input-count').textContent = inputCount;
            document.getElementById('search-count').textContent = searchCount;
            
            const efficiency = inputCount > 0 ? Math.round(((inputCount - searchCount) / inputCount) * 100) : 0;
            document.getElementById('debounce-ratio').textContent = `${efficiency}%`;
            
            if (lastDelay !== null) {
                document.getElementById('last-delay').textContent = `${lastDelay}ms`;
            }
        }

        function clearLogs() {
            inputCount = 0;
            searchCount = 0;
            lastInputTime = 0;
            
            const logContainer = document.getElementById('log-container');
            logContainer.innerHTML = `
                <div class="log-entry">
                    <span class="timestamp">Ready</span>
                    <span>Waiting for search input...</span>
                </div>
            `;
            
            updateStats();
            document.getElementById('last-delay').textContent = '-';
            document.getElementById('test-search-input').value = '';
        }

        // Initialize the test
        document.addEventListener('DOMContentLoaded', () => {
            setupDebouncedSearch();
            logEntry('Search debouncing test initialized', 'success');
        });
    </script>
</body>
</html>
