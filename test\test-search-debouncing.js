/**
 * Search Debouncing Test Suite
 * 
 * This test suite verifies that the search debouncing implementation
 * in popup.js is working correctly with proper timing and efficiency.
 */

console.log('=== LLMLog Search Debouncing Test Suite ===');

// Test configuration
const DEBOUNCE_DELAY = 300; // ms
const TEST_TOLERANCE = 50; // ms tolerance for timing tests

// Test state
let testResults = [];
let currentTest = 0;

// Mock chrome runtime for testing
if (typeof chrome === 'undefined') {
    window.chrome = {
        runtime: {
            sendMessage: function(message, callback) {
                // Mock the database response
                setTimeout(() => {
                    callback({
                        status: 'success',
                        data: [],
                        pagination: {
                            page: 1,
                            limit: 20,
                            hasMore: false,
                            totalPages: 0,
                            totalCount: 0,
                            search: message.payload?.search || ''
                        }
                    });
                }, 10); // Simulate small network delay
            }
        }
    };
}

/**
 * Test 1: Basic Debouncing Functionality
 * Verifies that search is delayed by the correct amount
 */
function testBasicDebouncing() {
    return new Promise((resolve) => {
        console.log('Test 1: Basic Debouncing Functionality');
        
        let searchExecuted = false;
        let startTime = Date.now();
        
        // Mock the handleSearch function
        const originalHandleSearch = window.handleSearch;
        window.handleSearch = function(searchTerm) {
            const elapsed = Date.now() - startTime;
            searchExecuted = true;
            
            const result = {
                test: 'Basic Debouncing',
                expected: DEBOUNCE_DELAY,
                actual: elapsed,
                tolerance: TEST_TOLERANCE,
                passed: Math.abs(elapsed - DEBOUNCE_DELAY) <= TEST_TOLERANCE,
                searchTerm: searchTerm
            };
            
            console.log(`  ✓ Search executed after ${elapsed}ms (expected ~${DEBOUNCE_DELAY}ms)`);
            console.log(`  ✓ Search term: "${searchTerm}"`);
            console.log(`  ✓ Test passed: ${result.passed}`);
            
            // Restore original function
            window.handleSearch = originalHandleSearch;
            
            resolve(result);
        };
        
        // Simulate typing
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = 'test search';
            searchInput.dispatchEvent(new Event('input'));
        } else {
            // Create a mock input for testing
            const mockInput = document.createElement('input');
            mockInput.id = 'search-input';
            document.body.appendChild(mockInput);
            
            // Simulate the debouncing logic from popup.js
            let searchTimeout;
            mockInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    const searchTerm = e.target.value.toLowerCase();
                    window.handleSearch(searchTerm);
                }, DEBOUNCE_DELAY);
            });
            
            mockInput.value = 'test search';
            mockInput.dispatchEvent(new Event('input'));
        }
        
        // Fallback timeout in case search doesn't execute
        setTimeout(() => {
            if (!searchExecuted) {
                resolve({
                    test: 'Basic Debouncing',
                    passed: false,
                    error: 'Search was not executed within expected timeframe'
                });
            }
        }, DEBOUNCE_DELAY + 200);
    });
}

/**
 * Test 2: Rapid Input Cancellation
 * Verifies that rapid typing cancels previous timeouts
 */
function testRapidInputCancellation() {
    return new Promise((resolve) => {
        console.log('Test 2: Rapid Input Cancellation');
        
        let searchCount = 0;
        let inputCount = 0;
        const startTime = Date.now();
        
        // Mock the handleSearch function
        const originalHandleSearch = window.handleSearch;
        window.handleSearch = function(searchTerm) {
            searchCount++;
            const elapsed = Date.now() - startTime;
            
            console.log(`  ✓ Search ${searchCount} executed after ${elapsed}ms: "${searchTerm}"`);
            
            // We expect only the last search to execute
            if (searchCount === 1) {
                const result = {
                    test: 'Rapid Input Cancellation',
                    inputCount: inputCount,
                    searchCount: searchCount,
                    finalSearchTerm: searchTerm,
                    passed: searchCount === 1 && searchTerm === 'test5',
                    efficiency: Math.round(((inputCount - searchCount) / inputCount) * 100)
                };
                
                console.log(`  ✓ Input events: ${inputCount}`);
                console.log(`  ✓ Search executions: ${searchCount}`);
                console.log(`  ✓ Debounce efficiency: ${result.efficiency}%`);
                console.log(`  ✓ Test passed: ${result.passed}`);
                
                // Restore original function
                window.handleSearch = originalHandleSearch;
                
                resolve(result);
            }
        };
        
        // Create mock input for testing
        const mockInput = document.createElement('input');
        mockInput.id = 'rapid-test-input';
        document.body.appendChild(mockInput);
        
        // Simulate the debouncing logic from popup.js
        let searchTimeout;
        mockInput.addEventListener('input', (e) => {
            inputCount++;
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchTerm = e.target.value.toLowerCase();
                window.handleSearch(searchTerm);
            }, DEBOUNCE_DELAY);
        });
        
        // Simulate rapid typing
        const searchTerms = ['t', 'te', 'tes', 'test', 'test5'];
        searchTerms.forEach((term, index) => {
            setTimeout(() => {
                mockInput.value = term;
                mockInput.dispatchEvent(new Event('input'));
            }, index * 50); // 50ms between keystrokes
        });
        
        // Cleanup timeout
        setTimeout(() => {
            mockInput.remove();
            if (searchCount === 0) {
                resolve({
                    test: 'Rapid Input Cancellation',
                    passed: false,
                    error: 'No search was executed'
                });
            }
        }, DEBOUNCE_DELAY + 500);
    });
}

/**
 * Test 3: Empty Search Handling
 * Verifies that empty searches are handled correctly
 */
function testEmptySearchHandling() {
    return new Promise((resolve) => {
        console.log('Test 3: Empty Search Handling');
        
        let searchExecuted = false;
        
        // Mock the handleSearch function
        const originalHandleSearch = window.handleSearch;
        window.handleSearch = function(searchTerm) {
            searchExecuted = true;
            
            const result = {
                test: 'Empty Search Handling',
                searchTerm: searchTerm,
                isEmpty: searchTerm === '',
                passed: true // Empty searches should be allowed
            };
            
            console.log(`  ✓ Empty search executed: "${searchTerm}"`);
            console.log(`  ✓ Test passed: ${result.passed}`);
            
            // Restore original function
            window.handleSearch = originalHandleSearch;
            
            resolve(result);
        };
        
        // Create mock input for testing
        const mockInput = document.createElement('input');
        mockInput.id = 'empty-test-input';
        document.body.appendChild(mockInput);
        
        // Simulate the debouncing logic from popup.js
        let searchTimeout;
        mockInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchTerm = e.target.value.toLowerCase();
                window.handleSearch(searchTerm);
            }, DEBOUNCE_DELAY);
        });
        
        // Simulate clearing the input
        mockInput.value = '';
        mockInput.dispatchEvent(new Event('input'));
        
        // Cleanup timeout
        setTimeout(() => {
            mockInput.remove();
            if (!searchExecuted) {
                resolve({
                    test: 'Empty Search Handling',
                    passed: false,
                    error: 'Empty search was not executed'
                });
            }
        }, DEBOUNCE_DELAY + 200);
    });
}

/**
 * Test 4: Search Term Processing
 * Verifies that search terms are processed correctly (lowercase, trimmed)
 */
function testSearchTermProcessing() {
    return new Promise((resolve) => {
        console.log('Test 4: Search Term Processing');
        
        const testCases = [
            { input: 'TEST', expected: 'test' },
            { input: '  spaced  ', expected: '  spaced  ' }, // Should preserve internal spaces
            { input: 'MiXeD CaSe', expected: 'mixed case' },
            { input: '123 Numbers!', expected: '123 numbers!' }
        ];
        
        let currentCase = 0;
        const results = [];
        
        // Mock the handleSearch function
        const originalHandleSearch = window.handleSearch;
        window.handleSearch = function(searchTerm) {
            const testCase = testCases[currentCase];
            const passed = searchTerm === testCase.expected;
            
            results.push({
                input: testCase.input,
                expected: testCase.expected,
                actual: searchTerm,
                passed: passed
            });
            
            console.log(`  ✓ Input: "${testCase.input}" → Output: "${searchTerm}" (Expected: "${testCase.expected}")`);
            console.log(`  ✓ Case ${currentCase + 1} passed: ${passed}`);
            
            currentCase++;
            
            if (currentCase >= testCases.length) {
                const allPassed = results.every(r => r.passed);
                
                const result = {
                    test: 'Search Term Processing',
                    cases: results,
                    passed: allPassed
                };
                
                console.log(`  ✓ All cases passed: ${allPassed}`);
                
                // Restore original function
                window.handleSearch = originalHandleSearch;
                
                resolve(result);
            } else {
                // Test next case
                setTimeout(() => testNextCase(), 100);
            }
        };
        
        function testNextCase() {
            const testCase = testCases[currentCase];
            
            // Create mock input for testing
            const mockInput = document.createElement('input');
            mockInput.id = `processing-test-input-${currentCase}`;
            document.body.appendChild(mockInput);
            
            // Simulate the debouncing logic from popup.js
            let searchTimeout;
            mockInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    const searchTerm = e.target.value.toLowerCase();
                    window.handleSearch(searchTerm);
                }, DEBOUNCE_DELAY);
            });
            
            mockInput.value = testCase.input;
            mockInput.dispatchEvent(new Event('input'));
            
            // Cleanup
            setTimeout(() => {
                mockInput.remove();
            }, DEBOUNCE_DELAY + 100);
        }
        
        // Start with first test case
        testNextCase();
    });
}

/**
 * Run all tests
 */
async function runAllTests() {
    console.log('Starting search debouncing test suite...\n');
    
    const tests = [
        testBasicDebouncing,
        testRapidInputCancellation,
        testEmptySearchHandling,
        testSearchTermProcessing
    ];
    
    for (const test of tests) {
        try {
            const result = await test();
            testResults.push(result);
            console.log(`✅ ${result.test}: ${result.passed ? 'PASSED' : 'FAILED'}\n`);
        } catch (error) {
            console.error(`❌ Test failed with error:`, error);
            testResults.push({
                test: test.name,
                passed: false,
                error: error.message
            });
        }
    }
    
    // Print summary
    console.log('=== Test Summary ===');
    const passedTests = testResults.filter(r => r.passed).length;
    const totalTests = testResults.length;
    
    console.log(`Passed: ${passedTests}/${totalTests}`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Search debouncing is working correctly.');
    } else {
        console.log('⚠️ Some tests failed. Please review the implementation.');
        
        testResults.filter(r => !r.passed).forEach(result => {
            console.log(`❌ ${result.test}: ${result.error || 'Test failed'}`);
        });
    }
    
    return testResults;
}

// Export for use in browser console or test runner
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runAllTests,
        testBasicDebouncing,
        testRapidInputCancellation,
        testEmptySearchHandling,
        testSearchTermProcessing
    };
}

// Auto-run if loaded in browser
if (typeof window !== 'undefined') {
    window.runSearchDebouncingTests = runAllTests;
    console.log('Search debouncing tests loaded. Run runSearchDebouncingTests() to start.');
}
