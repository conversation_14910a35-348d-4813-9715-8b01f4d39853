<!DOCTYPE html>
<html lang="en">
<head>
    <title>UI Positioning Test - LLMLog</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../popup.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: #4a90e2;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .control-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .test-button {
            padding: 10px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            background: #f0f0f0;
            border-color: #bbb;
        }
        
        .test-area {
            padding: 20px;
            min-height: 400px;
            position: relative;
        }
        
        .mock-conversation-list {
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 6px;
            overflow-y: auto;
            position: relative;
        }
        
        .mock-conversation-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background: #f9f9f9;
            margin: 5px;
            border-radius: 4px;
        }
        
        .mock-conversation-item:last-child {
            border-bottom: none;
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .instructions h4 {
            margin: 0 0 10px 0;
            color: #1565c0;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
            color: #1565c0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>UI Positioning Test</h1>
            <p>Testing toast notifications and load more button positioning</p>
        </div>
        
        <div class="test-controls">
            <div class="control-group">
                <h3>Toast Notification Tests</h3>
                <div class="control-buttons">
                    <button class="test-button" onclick="testSingleToast()">Single Toast</button>
                    <button class="test-button" onclick="testMultipleToasts()">Multiple Toasts</button>
                    <button class="test-button" onclick="testLoadedMessage()">Loaded 20 Conversations</button>
                    <button class="test-button" onclick="clearToasts()">Clear All Toasts</button>
                </div>
            </div>
            
            <div class="control-group">
                <h3>Load More Button Tests</h3>
                <div class="control-buttons">
                    <button class="test-button" onclick="showLoadMoreButton()">Show Load More</button>
                    <button class="test-button" onclick="hideLoadMoreButton()">Hide Load More</button>
                    <button class="test-button" onclick="testLoadMoreInVirtualScroll()">Virtual Scroll Load More</button>
                </div>
            </div>
        </div>
        
        <div class="test-area">
            <h3>Mock Conversation List</h3>
            <div class="mock-conversation-list" id="mock-list">
                <div class="mock-conversation-item">Conversation 1: How to center elements in CSS</div>
                <div class="mock-conversation-item">Conversation 2: JavaScript async/await patterns</div>
                <div class="mock-conversation-item">Conversation 3: React component optimization</div>
                <div class="mock-conversation-item">Conversation 4: Database indexing strategies</div>
                <div class="mock-conversation-item">Conversation 5: API design best practices</div>
                <div class="mock-conversation-item">Conversation 6: Performance monitoring tools</div>
                <div class="mock-conversation-item">Conversation 7: Security vulnerability testing</div>
                <div class="mock-conversation-item">Conversation 8: Mobile responsive design</div>
                <div class="mock-conversation-item">Conversation 9: Version control workflows</div>
                <div class="mock-conversation-item">Conversation 10: Testing automation strategies</div>
            </div>
            
            <div class="instructions">
                <h4>Expected Behavior:</h4>
                <ul>
                    <li><strong>Toast Notifications:</strong> Should appear at the bottom center of the screen</li>
                    <li><strong>Multiple Toasts:</strong> Should stack vertically above each other</li>
                    <li><strong>Load More Button:</strong> Should be centered horizontally in the container</li>
                    <li><strong>Virtual Scroll Load More:</strong> Should be sticky at bottom and centered</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Include required libraries -->
    <script src="../lib/dompurify.min.js"></script>
    
    <script>
        // Helper function to escape HTML
        function escapeHTML(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
    
    <script src="test-ui-positioning.js"></script>
</body>
</html>
