<!DOCTYPE html>
<html lang="en">
<head>
    <title>DOM Optimization Test - LLMLog</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../popup.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: #4a90e2;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .control-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .test-button {
            padding: 10px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            background: #f0f0f0;
            border-color: #bbb;
        }
        
        .test-area {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            min-height: 400px;
        }
        
        .test-section {
            border: 1px solid #ddd;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .test-section h4 {
            margin: 0;
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }
        
        .test-content {
            padding: 15px;
            height: 300px;
            overflow-y: auto;
        }
        
        .performance-metrics {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #4a90e2;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        
        .test-item {
            padding: 10px;
            margin: 5px 0;
            background: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }
        
        .test-item:hover {
            background: #e9ecef;
        }
        
        .optimized {
            border-left: 4px solid #28a745;
        }
        
        .unoptimized {
            border-left: 4px solid #dc3545;
        }
        
        .performance-log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-fast {
            color: #28a745;
        }
        
        .log-slow {
            color: #dc3545;
        }
        
        .log-medium {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>DOM Optimization Test</h1>
            <p>Testing virtual scrolling, template rendering, batch updates, and performance optimizations</p>
        </div>
        
        <div class="test-controls">
            <div class="control-group">
                <h3>Rendering Performance Tests</h3>
                <div class="control-buttons">
                    <button class="test-button" onclick="testOptimizedRendering(100)">100 Items (Optimized)</button>
                    <button class="test-button" onclick="testUnoptimizedRendering(100)">100 Items (Unoptimized)</button>
                    <button class="test-button" onclick="testOptimizedRendering(500)">500 Items (Optimized)</button>
                    <button class="test-button" onclick="testUnoptimizedRendering(500)">500 Items (Unoptimized)</button>
                    <button class="test-button" onclick="testOptimizedRendering(1000)">1000 Items (Optimized)</button>
                    <button class="test-button" onclick="testUnoptimizedRendering(1000)">1000 Items (Unoptimized)</button>
                </div>
            </div>
            
            <div class="control-group">
                <h3>Template System Tests</h3>
                <div class="control-buttons">
                    <button class="test-button" onclick="testTemplateRendering()">Template vs Manual</button>
                    <button class="test-button" onclick="testBatchUpdates()">Batch vs Individual</button>
                    <button class="test-button" onclick="testDocumentFragment()">Fragment vs Direct</button>
                </div>
            </div>
            
            <div class="control-group">
                <h3>Utility Tests</h3>
                <div class="control-buttons">
                    <button class="test-button" onclick="testDebouncing()">Debounce Function</button>
                    <button class="test-button" onclick="testThrottling()">Throttle Function</button>
                    <button class="test-button" onclick="clearTests()">Clear All</button>
                </div>
            </div>
        </div>
        
        <div class="test-area">
            <div class="test-section">
                <h4>Optimized Rendering</h4>
                <div id="optimized-content" class="test-content">
                    <!-- Optimized content will be rendered here -->
                </div>
            </div>
            
            <div class="test-section">
                <h4>Unoptimized Rendering</h4>
                <div id="unoptimized-content" class="test-content">
                    <!-- Unoptimized content will be rendered here -->
                </div>
            </div>
        </div>
        
        <div class="performance-metrics">
            <h2>Performance Metrics</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="optimized-time">0ms</div>
                    <div class="metric-label">Optimized Render Time</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="unoptimized-time">0ms</div>
                    <div class="metric-label">Unoptimized Render Time</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="performance-ratio">0x</div>
                    <div class="metric-label">Performance Improvement</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="dom-nodes">0</div>
                    <div class="metric-label">DOM Nodes Created</div>
                </div>
            </div>
            
            <div class="performance-log" id="performance-log">
                <div class="log-entry">Performance log will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Include required libraries -->
    <script src="../lib/dompurify.min.js"></script>
    
    <script>
        // Helper function to escape HTML
        function escapeHTML(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Mock global variables
        let allConversations = [];
        let virtualScrollList = null;
        let accessibilityManager = null;
        
        // Performance tracking
        let optimizedTime = 0;
        let unoptimizedTime = 0;
        let domNodeCount = 0;
    </script>
    
    <script src="test-dom-optimization.js"></script>
</body>
</html>
