<!DOCTYPE html>
<html lang="en">
<head>
    <title>Beautiful Card Design Test - LLMLog</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../popup.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .test-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .test-controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .control-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .test-button {
            padding: 10px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-button:hover {
            background: #4a90e2;
            color: white;
            border-color: #4a90e2;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .conversation-list-container {
            height: 500px;
            position: relative;
            background: #f4f6f8;
        }
        
        .instructions {
            padding: 20px;
            background: #e8f4fd;
            border-top: 1px solid #bee5eb;
            text-align: center;
        }
        
        .instructions h3 {
            margin: 0 0 12px 0;
            color: #0c5460;
            font-size: 16px;
        }
        
        .instructions ul {
            margin: 0;
            padding: 0;
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            justify-content: center;
        }
        
        .instructions li {
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            color: #0c5460;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 16px;
            text-align: center;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎨 Beautiful Card Design</h1>
            <p>Testing the new conversation card layout and load more button centering</p>
        </div>
        
        <div class="test-controls">
            <div class="control-buttons">
                <button class="test-button" onclick="generateCards(5)">5 Cards</button>
                <button class="test-button" onclick="generateCards(10)">10 Cards</button>
                <button class="test-button" onclick="generateCards(20)">20 Cards</button>
                <button class="test-button" onclick="showLoadMore()">Show Load More</button>
                <button class="test-button" onclick="clearCards()">Clear All</button>
            </div>
        </div>
        
        <div class="conversation-list-container">
            <div id="conversation-list" class="virtual-scroll-viewport">
                <!-- Cards will be generated here -->
            </div>
        </div>
        
        <div class="feature-highlight">
            ✨ Updated Features: Date in top-right, integrated delete button, perfectly centered load more
        </div>

        <div class="instructions">
            <h3>Card Design Features</h3>
            <ul>
                <li>📅 Date in top-right corner</li>
                <li>🗑️ Integrated delete button</li>
                <li>🎯 Perfectly centered load more</li>
                <li>🎨 Gradient backgrounds</li>
                <li>✨ Hover animations</li>
                <li>🏷️ Beautiful platform badges</li>
                <li>📝 Two-line preview text</li>
                <li>📱 Responsive design</li>
            </ul>
        </div>
    </div>

    <script>
        // Helper function to escape HTML
        function escapeHTML(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Sample conversation data
        const sampleConversations = [
            {
                id: 1,
                platform: 'ChatGPT',
                title: 'How to create beautiful CSS cards',
                prompt: 'I want to create beautiful card designs for my web application. Can you help me with modern CSS techniques including gradients, shadows, and hover effects?',
                createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString()
            },
            {
                id: 2,
                platform: 'Claude',
                title: 'JavaScript async/await best practices',
                prompt: 'What are the best practices for using async/await in JavaScript? I want to handle errors properly and avoid common pitfalls.',
                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString()
            },
            {
                id: 3,
                platform: 'Gemini',
                title: 'React component optimization strategies',
                prompt: 'How can I optimize my React components for better performance? I\'m dealing with large lists and frequent re-renders.',
                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString()
            },
            {
                id: 4,
                platform: 'ChatGPT',
                title: 'Database indexing for performance',
                prompt: 'Explain database indexing strategies for improving query performance. What are the trade-offs between different index types?',
                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString()
            },
            {
                id: 5,
                platform: 'Claude',
                title: 'API design principles and patterns',
                prompt: 'What are the key principles for designing RESTful APIs? How do I handle versioning, authentication, and error responses?',
                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString()
            }
        ];
        
        function generateCards(count) {
            const container = document.getElementById('conversation-list');
            container.innerHTML = '';
            
            for (let i = 0; i < count; i++) {
                const conv = sampleConversations[i % sampleConversations.length];
                const card = createConversationCard({
                    ...conv,
                    id: i + 1,
                    title: `${conv.title} (${i + 1})`
                });
                container.appendChild(card);
            }
            
            console.log(`Generated ${count} beautiful cards`);
        }
        
        function createConversationCard(conversation) {
            const item = document.createElement('div');
            item.className = 'conversation-item virtual-item';
            item.setAttribute('data-conversation-id', conversation.id);
            item.setAttribute('role', 'listitem');
            item.setAttribute('tabindex', '0');
            
            const dateString = new Date(conversation.createdAt).toLocaleString();
            
            item.innerHTML = `
                <div class="item-date">${dateString}</div>
                <div class="item-header">
                    <span class="platform-badge ${conversation.platform.toLowerCase()}">${conversation.platform}</span>
                    <span class="item-title">${escapeHTML(conversation.title)}</span>
                </div>
                <div class="item-preview">
                    <strong>You:</strong> ${escapeHTML(conversation.prompt)}
                </div>
                <button class="delete-button" aria-label="Delete conversation" title="Delete">🗑️</button>
            `;

            // Add delete button event listener
            const deleteButton = item.querySelector('.delete-button');
            deleteButton.addEventListener('click', (e) => {
                e.stopPropagation();
                if (confirm(`Delete "${conversation.title}"?`)) {
                    item.remove();
                    console.log(`Deleted: ${conversation.title}`);
                }
            });
            
            // Add click handler
            item.addEventListener('click', () => {
                alert(`Clicked: ${conversation.title}`);
            });
            
            return item;
        }
        
        function showLoadMore() {
            const container = document.getElementById('conversation-list');
            
            // Remove existing load more button
            const existingButton = container.querySelector('.load-more-button');
            if (existingButton) {
                existingButton.remove();
            }
            
            // Create load more button
            const loadMoreButton = document.createElement('button');
            loadMoreButton.className = 'load-more-button';
            loadMoreButton.innerHTML = '📚 Load More Conversations';
            
            loadMoreButton.addEventListener('click', () => {
                loadMoreButton.innerHTML = '⏳ Loading...';
                loadMoreButton.disabled = true;
                
                setTimeout(() => {
                    const currentCount = container.querySelectorAll('.conversation-item').length;
                    generateCards(currentCount + 5);
                    showLoadMore(); // Re-add the button
                }, 1500);
            });
            
            container.appendChild(loadMoreButton);
            console.log('Load more button added and centered');
        }
        
        function clearCards() {
            const container = document.getElementById('conversation-list');
            container.innerHTML = '';
            console.log('All cards cleared');
        }
        
        // Initialize with some cards
        document.addEventListener('DOMContentLoaded', () => {
            generateCards(8);
            showLoadMore();
            console.log('Beautiful card design test loaded');
        });
    </script>
</body>
</html>
