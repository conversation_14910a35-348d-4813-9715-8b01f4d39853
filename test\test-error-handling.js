/**
 * Error Handling and User Feedback Test Script
 * Tests toast notifications, loading states, confirmation dialogs, and error handling
 */

// Copy the UIFeedback class from popup.js
class UIFeedback {
  static showToast(message, type = 'info', duration = 4000) {
    // Remove existing toasts of the same type
    const existingToasts = document.querySelectorAll(`.toast.toast-${type}`);
    existingToasts.forEach(toast => toast.remove());
    
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    // Create toast content
    const content = document.createElement('div');
    content.className = 'toast-content';
    content.textContent = message;
    
    // Create close button
    const closeButton = document.createElement('button');
    closeButton.className = 'toast-close';
    closeButton.innerHTML = '×';
    closeButton.setAttribute('aria-label', 'Close notification');
    closeButton.addEventListener('click', () => {
      this.hideToast(toast);
    });
    
    toast.appendChild(content);
    toast.appendChild(closeButton);
    
    // Add to DOM
    document.body.appendChild(toast);
    
    // Trigger animation
    setTimeout(() => {
      toast.classList.add('toast-show');
    }, 10);
    
    // Auto-hide after duration
    if (duration > 0) {
      setTimeout(() => {
        this.hideToast(toast);
      }, duration);
    }
    
    return toast;
  }
  
  static hideToast(toast) {
    if (toast && toast.parentNode) {
      toast.classList.add('toast-hide');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.remove();
        }
      }, 300);
    }
  }
  
  static showLoadingState(element, message = 'Loading...') {
    if (!element) return;
    
    element.classList.add('loading-state');
    element.setAttribute('aria-busy', 'true');
    
    // Store original content
    if (!element.dataset.originalContent) {
      element.dataset.originalContent = element.innerHTML;
    }
    
    // Add loading indicator
    element.innerHTML = `
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <span class="loading-text">${message}</span>
      </div>
    `;
    
    // Disable if it's a button
    if (element.tagName === 'BUTTON') {
      element.disabled = true;
    }
  }
  
  static hideLoadingState(element) {
    if (!element) return;
    
    element.classList.remove('loading-state');
    element.removeAttribute('aria-busy');
    
    // Restore original content
    if (element.dataset.originalContent) {
      element.innerHTML = element.dataset.originalContent;
      delete element.dataset.originalContent;
    }
    
    // Re-enable if it's a button
    if (element.tagName === 'BUTTON') {
      element.disabled = false;
    }
  }
  
  static showConfirmDialog(message, title = 'Confirm Action', options = {}) {
    return new Promise((resolve) => {
      const dialog = document.createElement('div');
      dialog.className = 'confirm-dialog-overlay';
      dialog.setAttribute('role', 'dialog');
      dialog.setAttribute('aria-modal', 'true');
      dialog.setAttribute('aria-labelledby', 'dialog-title');
      dialog.setAttribute('aria-describedby', 'dialog-message');
      
      dialog.innerHTML = `
        <div class="confirm-dialog">
          <div class="dialog-header">
            <h3 id="dialog-title">${escapeHTML(title)}</h3>
          </div>
          <div class="dialog-body">
            <p id="dialog-message">${escapeHTML(message)}</p>
          </div>
          <div class="dialog-footer">
            <button class="dialog-button dialog-button-cancel" type="button">
              ${options.cancelText || 'Cancel'}
            </button>
            <button class="dialog-button dialog-button-confirm" type="button">
              ${options.confirmText || 'Confirm'}
            </button>
          </div>
        </div>
      `;
      
      // Add event listeners
      const cancelButton = dialog.querySelector('.dialog-button-cancel');
      const confirmButton = dialog.querySelector('.dialog-button-confirm');
      
      const cleanup = () => {
        dialog.remove();
        document.body.classList.remove('dialog-open');
      };
      
      cancelButton.addEventListener('click', () => {
        cleanup();
        resolve(false);
      });
      
      confirmButton.addEventListener('click', () => {
        cleanup();
        resolve(true);
      });
      
      // Close on escape
      const handleKeyDown = (e) => {
        if (e.key === 'Escape') {
          cleanup();
          resolve(false);
          document.removeEventListener('keydown', handleKeyDown);
        }
      };
      
      document.addEventListener('keydown', handleKeyDown);
      
      // Close on overlay click
      dialog.addEventListener('click', (e) => {
        if (e.target === dialog) {
          cleanup();
          resolve(false);
        }
      });
      
      // Add to DOM and show
      document.body.appendChild(dialog);
      document.body.classList.add('dialog-open');
      
      // Focus the confirm button
      setTimeout(() => {
        confirmButton.focus();
      }, 100);
    });
  }
  
  static showErrorMessage(error, context = '') {
    let message = 'An unexpected error occurred.';
    
    if (typeof error === 'string') {
      message = error;
    } else if (error && error.message) {
      message = error.message;
    }
    
    if (context) {
      message = `${context}: ${message}`;
    }
    
    console.error('Error:', error);
    this.showToast(message, 'error', 6000);
  }
  
  static showSuccessMessage(message) {
    this.showToast(message, 'success', 3000);
  }
  
  static showWarningMessage(message) {
    this.showToast(message, 'warning', 4000);
  }
  
  static showInfoMessage(message) {
    this.showToast(message, 'info', 3000);
  }
}

// Test functions
function logTest(message, type = 'info') {
  const log = document.getElementById('test-log');
  const item = document.createElement('div');
  item.className = `result-${type}`;
  item.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
  log.appendChild(item);
  log.scrollTop = log.scrollHeight;
}

// Toast notification tests
function testSuccessToast() {
  UIFeedback.showSuccessMessage('Operation completed successfully!');
  logTest('Success toast displayed', 'success');
}

function testErrorToast() {
  UIFeedback.showErrorMessage('Something went wrong. Please try again.');
  logTest('Error toast displayed', 'error');
}

function testWarningToast() {
  UIFeedback.showWarningMessage('This action cannot be undone.');
  logTest('Warning toast displayed', 'info');
}

function testInfoToast() {
  UIFeedback.showInfoMessage('Here is some helpful information.');
  logTest('Info toast displayed', 'info');
}

function testLongToast() {
  UIFeedback.showToast('This is a very long message that should wrap properly and still look good in the toast notification. It contains multiple sentences and should demonstrate how longer content is handled.', 'info', 8000);
  logTest('Long message toast displayed', 'info');
}

function testMultipleToasts() {
  UIFeedback.showSuccessMessage('First message');
  setTimeout(() => UIFeedback.showWarningMessage('Second message'), 500);
  setTimeout(() => UIFeedback.showErrorMessage('Third message'), 1000);
  setTimeout(() => UIFeedback.showInfoMessage('Fourth message'), 1500);
  logTest('Multiple toasts displayed in sequence', 'info');
}

// Loading state tests
function testButtonLoading(button) {
  UIFeedback.showLoadingState(button, 'Processing...');
  logTest('Button loading state applied', 'info');
  
  setTimeout(() => {
    UIFeedback.hideLoadingState(button);
    UIFeedback.showSuccessMessage('Button operation completed!');
    logTest('Button loading state removed', 'success');
  }, 3000);
}

function testElementLoading() {
  const element = document.getElementById('demo-element');
  UIFeedback.showLoadingState(element, 'Loading content...');
  logTest('Element loading state applied', 'info');
  
  setTimeout(() => {
    UIFeedback.hideLoadingState(element);
    UIFeedback.showSuccessMessage('Content loaded!');
    logTest('Element loading state removed', 'success');
  }, 4000);
}

function testCustomLoading() {
  const element = document.getElementById('demo-element');
  UIFeedback.showLoadingState(element, 'Analyzing data, please wait...');
  logTest('Custom loading message applied', 'info');
  
  setTimeout(() => {
    UIFeedback.hideLoadingState(element);
    UIFeedback.showSuccessMessage('Analysis complete!');
    logTest('Custom loading state removed', 'success');
  }, 5000);
}

// Confirmation dialog tests
async function testConfirmDialog() {
  logTest('Basic confirmation dialog opened', 'info');
  const result = await UIFeedback.showConfirmDialog(
    'Are you sure you want to proceed with this action?',
    'Confirm Action'
  );
  
  if (result) {
    UIFeedback.showSuccessMessage('Action confirmed!');
    logTest('User confirmed action', 'success');
  } else {
    UIFeedback.showInfoMessage('Action cancelled.');
    logTest('User cancelled action', 'info');
  }
}

async function testCustomConfirm() {
  logTest('Custom confirmation dialog opened', 'info');
  const result = await UIFeedback.showConfirmDialog(
    'This will permanently save your changes. Do you want to continue?',
    'Save Changes',
    {
      confirmText: 'Save',
      cancelText: 'Don\'t Save'
    }
  );
  
  if (result) {
    UIFeedback.showSuccessMessage('Changes saved!');
    logTest('User chose to save', 'success');
  } else {
    UIFeedback.showWarningMessage('Changes not saved.');
    logTest('User chose not to save', 'info');
  }
}

async function testDeleteConfirm() {
  logTest('Delete confirmation dialog opened', 'info');
  const result = await UIFeedback.showConfirmDialog(
    'Are you sure you want to delete this item? This action cannot be undone.',
    'Delete Item',
    {
      confirmText: 'Delete',
      cancelText: 'Keep'
    }
  );
  
  if (result) {
    UIFeedback.showSuccessMessage('Item deleted successfully!');
    logTest('User confirmed deletion', 'success');
  } else {
    UIFeedback.showInfoMessage('Item kept safely.');
    logTest('User cancelled deletion', 'info');
  }
}

// Error scenario tests
function testNetworkError() {
  const error = new Error('Network request failed');
  UIFeedback.showErrorMessage(error, 'Failed to load data');
  logTest('Network error displayed', 'error');
}

function testValidationError() {
  UIFeedback.showErrorMessage('Please fill in all required fields before submitting.', 'Validation Error');
  logTest('Validation error displayed', 'error');
}

function testUnknownError() {
  UIFeedback.showErrorMessage('An unexpected error occurred. Please try again later.');
  logTest('Unknown error displayed', 'error');
}

function testErrorWithContext() {
  const error = { message: 'Database connection timeout' };
  UIFeedback.showErrorMessage(error, 'Failed to save conversation');
  logTest('Error with context displayed', 'error');
}

// Simulate async operations
function simulateAsyncOperation(button, message) {
  UIFeedback.showLoadingState(button, message);
  logTest(`Async operation started: ${message}`, 'info');
  
  // Simulate random success/failure
  const success = Math.random() > 0.3;
  const duration = 2000 + Math.random() * 3000;
  
  setTimeout(() => {
    UIFeedback.hideLoadingState(button);
    
    if (success) {
      UIFeedback.showSuccessMessage('Operation completed successfully!');
      logTest('Async operation succeeded', 'success');
    } else {
      UIFeedback.showErrorMessage('Operation failed. Please try again.');
      logTest('Async operation failed', 'error');
    }
  }, duration);
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
  logTest('Error handling test page loaded', 'success');
  
  // Test keyboard shortcuts for dialogs
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 't') {
      e.preventDefault();
      testConfirmDialog();
    }
  });
  
  console.log('Error handling test loaded. Use the buttons to test different scenarios.');
  console.log('Keyboard shortcut: Ctrl+T to test confirmation dialog');
});
