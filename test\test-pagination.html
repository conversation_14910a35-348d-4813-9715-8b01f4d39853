<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagination Test - LLMLog</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        
        .test-controls input, .test-controls button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .test-controls button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        .test-controls button:hover {
            background: #0056b3;
        }
        
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .conversation-item {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
        }
        
        .conversation-meta {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .pagination-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>LLMLog Pagination Test</h1>
    <p>This page tests the pagination functionality of the LLMLog extension.</p>
    
    <div class="test-section">
        <h2>Test Pagination</h2>
        <div class="test-controls">
            <input type="number" id="page" placeholder="Page" value="1" min="1">
            <input type="number" id="limit" placeholder="Limit" value="10" min="1" max="100">
            <input type="text" id="search" placeholder="Search term">
            <button onclick="testGetConversations()">Get Conversations</button>
            <button onclick="testGetCount()">Get Count</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="results" class="results" style="display: none;">
            <h3>Results</h3>
            <div id="pagination-info" class="pagination-info"></div>
            <div id="conversations-list"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Test Scenarios</h2>
        <div class="test-controls">
            <button onclick="runTestScenario('basic')">Basic Pagination</button>
            <button onclick="runTestScenario('search')">Search Pagination</button>
            <button onclick="runTestScenario('edge')">Edge Cases</button>
            <button onclick="runTestScenario('all')">Run All Tests</button>
        </div>
        
        <div id="test-results" class="results" style="display: none;">
            <h3>Test Results</h3>
            <div id="test-output"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Instructions</h2>
        <ol>
            <li>Make sure the LLMLog extension is loaded and active</li>
            <li>Open the browser console to see detailed logs</li>
            <li>Use the controls above to test different pagination scenarios</li>
            <li>Check that the results match expected behavior</li>
        </ol>
        
        <h3>Expected Behavior:</h3>
        <ul>
            <li>Page 1 with limit 10 should return the first 10 conversations</li>
            <li>Search should filter conversations by title, prompt, response, or platform</li>
            <li>Pagination info should show correct page, limit, and hasMore status</li>
            <li>Edge cases should handle invalid inputs gracefully</li>
        </ul>
    </div>

    <script>
        // Test functions
        async function testGetConversations() {
            const page = parseInt(document.getElementById('page').value) || 1;
            const limit = parseInt(document.getElementById('limit').value) || 10;
            const search = document.getElementById('search').value || '';
            
            console.log('Testing getConversations with:', { page, limit, search });
            
            try {
                const response = await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        namespace: 'database',
                        action: 'getConversations',
                        payload: { page, limit, search }
                    }, resolve);
                });
                
                displayResults(response);
            } catch (error) {
                displayError('Error testing getConversations: ' + error.message);
            }
        }
        
        async function testGetCount() {
            const search = document.getElementById('search').value || '';
            
            console.log('Testing getTotalConversationCount with search:', search);
            
            try {
                const response = await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        namespace: 'database',
                        action: 'getTotalConversationCount',
                        payload: { search }
                    }, resolve);
                });
                
                displayCountResults(response);
            } catch (error) {
                displayError('Error testing getTotalConversationCount: ' + error.message);
            }
        }
        
        function displayResults(response) {
            const resultsDiv = document.getElementById('results');
            const paginationInfo = document.getElementById('pagination-info');
            const conversationsList = document.getElementById('conversations-list');
            
            resultsDiv.style.display = 'block';
            
            if (response.status === 'success') {
                const { data, pagination } = response;
                
                paginationInfo.innerHTML = `
                    <strong>Pagination Info:</strong><br>
                    Page: ${pagination.page}, Limit: ${pagination.limit}, 
                    Has More: ${pagination.hasMore}, Total Pages: ${pagination.totalPages || 'N/A'}, 
                    Total Count: ${pagination.totalCount || 'N/A'}, 
                    Search: "${pagination.search || 'none'}"
                `;
                
                conversationsList.innerHTML = '';
                if (data.length === 0) {
                    conversationsList.innerHTML = '<p>No conversations found.</p>';
                } else {
                    data.forEach((conv, index) => {
                        const item = document.createElement('div');
                        item.className = 'conversation-item';
                        item.innerHTML = `
                            <div class="conversation-meta">
                                #${index + 1} | ID: ${conv.id} | Platform: ${conv.platform} | 
                                Date: ${new Date(conv.createdAt).toLocaleString()}
                            </div>
                            <strong>${conv.title}</strong><br>
                            <em>Prompt:</em> ${conv.prompt.substring(0, 100)}...
                        `;
                        conversationsList.appendChild(item);
                    });
                }
            } else {
                displayError('API Error: ' + response.message);
            }
        }
        
        function displayCountResults(response) {
            const resultsDiv = document.getElementById('results');
            const paginationInfo = document.getElementById('pagination-info');
            const conversationsList = document.getElementById('conversations-list');
            
            resultsDiv.style.display = 'block';
            
            if (response.status === 'success') {
                paginationInfo.innerHTML = `
                    <strong>Total Count:</strong> ${response.data.totalCount}
                `;
                conversationsList.innerHTML = '<p>Count query - no conversations displayed.</p>';
            } else {
                displayError('Count API Error: ' + response.message);
            }
        }
        
        function displayError(message) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            resultsDiv.className = 'results error';
            resultsDiv.innerHTML = `<h3>Error</h3><p>${message}</p>`;
        }
        
        function clearResults() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'none';
            resultsDiv.className = 'results';
        }
        
        async function runTestScenario(scenario) {
            const testResults = document.getElementById('test-results');
            const testOutput = document.getElementById('test-output');
            
            testResults.style.display = 'block';
            testOutput.innerHTML = '<p>Running tests...</p>';
            
            const results = [];
            
            try {
                if (scenario === 'basic' || scenario === 'all') {
                    results.push(await runBasicTests());
                }
                
                if (scenario === 'search' || scenario === 'all') {
                    results.push(await runSearchTests());
                }
                
                if (scenario === 'edge' || scenario === 'all') {
                    results.push(await runEdgeTests());
                }
                
                const allPassed = results.every(r => r.passed);
                testResults.className = allPassed ? 'results success' : 'results error';
                
                testOutput.innerHTML = `
                    <h4>Test Results Summary</h4>
                    <p><strong>Overall:</strong> ${allPassed ? 'PASSED' : 'FAILED'}</p>
                    <pre>${results.map(r => r.output).join('\n\n')}</pre>
                `;
                
            } catch (error) {
                testResults.className = 'results error';
                testOutput.innerHTML = `<p><strong>Test Error:</strong> ${error.message}</p>`;
            }
        }
        
        async function runBasicTests() {
            const tests = [
                { page: 1, limit: 5, description: 'First page, 5 items' },
                { page: 2, limit: 5, description: 'Second page, 5 items' },
                { page: 1, limit: 20, description: 'First page, 20 items' }
            ];
            
            const results = [];
            
            for (const test of tests) {
                try {
                    const response = await new Promise((resolve) => {
                        chrome.runtime.sendMessage({
                            namespace: 'database',
                            action: 'getConversations',
                            payload: test
                        }, resolve);
                    });
                    
                    const passed = response.status === 'success' && 
                                 response.pagination.page === test.page &&
                                 response.pagination.limit === test.limit;
                    
                    results.push(`${test.description}: ${passed ? 'PASS' : 'FAIL'}`);
                } catch (error) {
                    results.push(`${test.description}: ERROR - ${error.message}`);
                }
            }
            
            return {
                passed: results.every(r => r.includes('PASS')),
                output: 'Basic Tests:\n' + results.join('\n')
            };
        }
        
        async function runSearchTests() {
            const tests = [
                { search: 'test', description: 'Search for "test"' },
                { search: 'ChatGPT', description: 'Search for platform "ChatGPT"' },
                { search: 'nonexistent123', description: 'Search for non-existent term' }
            ];
            
            const results = [];
            
            for (const test of tests) {
                try {
                    const response = await new Promise((resolve) => {
                        chrome.runtime.sendMessage({
                            namespace: 'database',
                            action: 'getConversations',
                            payload: { page: 1, limit: 10, search: test.search }
                        }, resolve);
                    });
                    
                    const passed = response.status === 'success' && 
                                 response.pagination.search === test.search;
                    
                    results.push(`${test.description}: ${passed ? 'PASS' : 'FAIL'} (${response.data.length} results)`);
                } catch (error) {
                    results.push(`${test.description}: ERROR - ${error.message}`);
                }
            }
            
            return {
                passed: results.every(r => r.includes('PASS')),
                output: 'Search Tests:\n' + results.join('\n')
            };
        }
        
        async function runEdgeTests() {
            const tests = [
                { page: 0, limit: 10, description: 'Page 0 (should default to 1)' },
                { page: -1, limit: 10, description: 'Negative page' },
                { page: 1, limit: 0, description: 'Zero limit' },
                { page: 999, limit: 10, description: 'Very high page number' }
            ];
            
            const results = [];
            
            for (const test of tests) {
                try {
                    const response = await new Promise((resolve) => {
                        chrome.runtime.sendMessage({
                            namespace: 'database',
                            action: 'getConversations',
                            payload: test
                        }, resolve);
                    });
                    
                    const passed = response.status === 'success';
                    results.push(`${test.description}: ${passed ? 'PASS' : 'FAIL'}`);
                } catch (error) {
                    results.push(`${test.description}: ERROR - ${error.message}`);
                }
            }
            
            return {
                passed: results.every(r => r.includes('PASS')),
                output: 'Edge Case Tests:\n' + results.join('\n')
            };
        }
        
        // Check if extension is available
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            document.body.innerHTML = `
                <div class="test-section error">
                    <h2>Extension Not Available</h2>
                    <p>This test page requires the LLMLog extension to be loaded and active.</p>
                    <p>Please load the extension and refresh this page.</p>
                </div>
            `;
        } else {
            console.log('LLMLog extension detected. Test page ready.');
        }
    </script>
</body>
</html>
