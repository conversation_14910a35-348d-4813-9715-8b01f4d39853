<!DOCTYPE html>
<html lang="en">
<head>
    <title>Tailwind CSS Redesign Test - LLMLog</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../lib/tailwind.css">
    <link rel="stylesheet" href="../popup.css">
    <style>
        body {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .test-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .test-controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .control-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .test-button {
            padding: 10px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-button:hover {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .conversation-list-container {
            height: 500px;
            position: relative;
            background: #f9fafb;
        }
        
        .instructions {
            padding: 20px;
            background: #dbeafe;
            border-top: 1px solid #93c5fd;
            text-align: center;
        }
        
        .instructions h3 {
            margin: 0 0 12px 0;
            color: #1e40af;
            font-size: 16px;
        }
        
        .instructions ul {
            margin: 0;
            padding: 0;
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            justify-content: center;
        }
        
        .instructions li {
            background: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            color: #1e40af;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 16px;
            text-align: center;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎨 Tailwind CSS Redesign</h1>
            <p>Testing the new modern card-based layout with Tailwind CSS integration</p>
        </div>
        
        <div class="test-controls">
            <div class="control-buttons">
                <button class="test-button" onclick="generateCards(5)">5 Cards</button>
                <button class="test-button" onclick="generateCards(10)">10 Cards</button>
                <button class="test-button" onclick="generateCards(20)">20 Cards</button>
                <button class="test-button" onclick="showLoadMore()">Show Load More</button>
                <button class="test-button" onclick="showEndMessage()">Show End Message</button>
                <button class="test-button" onclick="clearCards()">Clear All</button>
            </div>
        </div>
        
        <div class="conversation-list-container">
            <div id="conversation-list" class="flex-1 overflow-y-auto p-2 space-y-3 h-full">
                <!-- Cards will be generated here -->
            </div>
        </div>
        
        <div class="feature-highlight">
            ✨ New Features: Tailwind CSS integration, modern card design, enhanced accessibility
        </div>

        <div class="instructions">
            <h3>Tailwind CSS Redesign Features</h3>
            <ul>
                <li>🎨 Tailwind CSS utility classes</li>
                <li>📅 Date in top-right corner</li>
                <li>🗑️ Integrated delete button</li>
                <li>🎯 Perfectly centered load more</li>
                <li>🌈 Modern gradient backgrounds</li>
                <li>✨ Smooth hover animations</li>
                <li>🏷️ Beautiful platform badges</li>
                <li>📝 Two-line preview text</li>
                <li>📱 Responsive design</li>
                <li>♿ Enhanced accessibility</li>
            </ul>
        </div>
    </div>

    <script>
        // Helper function to escape HTML
        function escapeHTML(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Sample conversation data
        const sampleConversations = [
            {
                id: 1,
                platform: 'ChatGPT',
                title: 'How to integrate Tailwind CSS with Chrome extensions',
                prompt: 'I want to integrate Tailwind CSS into my Chrome extension popup. What are the best practices for including Tailwind CSS locally and ensuring it works with Content Security Policy?',
                createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString()
            },
            {
                id: 2,
                platform: 'Claude',
                title: 'Modern card design principles with CSS',
                prompt: 'What are the key principles for creating modern, accessible card designs using CSS? I want to implement hover effects, proper spacing, and responsive layouts.',
                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString()
            },
            {
                id: 3,
                platform: 'Gemini',
                title: 'JavaScript DOM manipulation optimization',
                prompt: 'How can I optimize DOM manipulation in JavaScript for better performance? I\'m working with dynamic lists and need to minimize reflows and repaints.',
                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString()
            },
            {
                id: 4,
                platform: 'ChatGPT',
                title: 'Accessibility best practices for web interfaces',
                prompt: 'What are the essential accessibility features I should implement in my web interface? I want to ensure screen reader compatibility and keyboard navigation.',
                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString()
            },
            {
                id: 5,
                platform: 'Claude',
                title: 'CSS Grid vs Flexbox for responsive layouts',
                prompt: 'When should I use CSS Grid versus Flexbox for creating responsive layouts? What are the strengths and use cases for each approach?',
                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString()
            }
        ];
        
        function generateCards(count) {
            const container = document.getElementById('conversation-list');
            container.innerHTML = '';
            
            for (let i = 0; i < count; i++) {
                const conv = sampleConversations[i % sampleConversations.length];
                const card = createConversationCard({
                    ...conv,
                    id: i + 1,
                    title: `${conv.title} (${i + 1})`
                });
                container.appendChild(card);
            }
            
            console.log(`Generated ${count} modern Tailwind CSS cards`);
        }
        
        function createConversationCard(conversation) {
            const item = document.createElement('div');
            item.className = 'conversation-item virtual-item bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl p-4 pt-10 mx-3 my-2 cursor-pointer transition-all duration-300 shadow-sm hover:shadow-md hover:bg-gradient-to-br hover:from-white hover:to-blue-50 hover:border-blue-200 hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 relative overflow-hidden';
            item.setAttribute('data-conversation-id', conversation.id);
            item.setAttribute('role', 'listitem');
            item.setAttribute('tabindex', '0');
            
            const dateString = new Date(conversation.createdAt).toLocaleString();
            
            item.innerHTML = `
                <div class="item-date absolute top-3 right-3 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full font-medium z-10">${dateString}</div>
                <div class="item-header flex items-center mb-3 gap-3 pr-20">
                    <span class="platform-badge ${conversation.platform.toLowerCase()} text-xs font-semibold px-2.5 py-1 rounded-full text-white uppercase tracking-wide shadow-sm">${conversation.platform}</span>
                    <span class="item-title font-semibold text-gray-900 truncate flex-1">${escapeHTML(conversation.title)}</span>
                </div>
                <div class="item-preview text-sm text-gray-600 bg-blue-50 p-3 rounded-lg border-l-3 border-blue-200 mt-1 line-clamp-2">
                    <strong class="text-gray-700">You:</strong> ${escapeHTML(conversation.prompt)}
                </div>
                <button class="delete-button absolute right-3 bottom-3 w-9 h-7 bg-white/90 border border-red-200 text-red-500 rounded-lg flex items-center justify-center opacity-0 transform translate-y-2 transition-all duration-300 hover:bg-red-500 hover:text-white hover:scale-105 hover:shadow-md z-20">🗑️</button>
            `;

            // Add delete button event listener
            const deleteButton = item.querySelector('.delete-button');
            deleteButton.addEventListener('click', (e) => {
                e.stopPropagation();
                if (confirm(`Delete "${conversation.title}"?`)) {
                    item.remove();
                    console.log(`Deleted: ${conversation.title}`);
                }
            });
            
            // Add click handler
            item.addEventListener('click', () => {
                alert(`Clicked: ${conversation.title}`);
            });
            
            return item;
        }
        
        function showLoadMore() {
            const container = document.getElementById('conversation-list');
            
            // Remove existing load more button
            const existingButton = container.querySelector('.load-more-button');
            if (existingButton) {
                existingButton.remove();
            }
            
            // Create load more button with Tailwind classes
            const loadMoreButton = document.createElement('button');
            loadMoreButton.className = 'load-more-button bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:from-blue-600 hover:to-blue-700 hover:shadow-xl hover:-translate-y-0.5 active:translate-y-0 transition-all duration-300 uppercase tracking-wide text-sm sticky bottom-5 left-1/2 transform -translate-x-1/2 z-50 min-w-60 max-w-80 mx-auto';
            loadMoreButton.innerHTML = '📚 Load More Conversations';
            
            loadMoreButton.addEventListener('click', () => {
                loadMoreButton.innerHTML = '⏳ Loading...';
                loadMoreButton.disabled = true;
                
                setTimeout(() => {
                    const currentCount = container.querySelectorAll('.conversation-item').length;
                    generateCards(currentCount + 5);
                    showLoadMore(); // Re-add the button
                }, 1500);
            });
            
            container.appendChild(loadMoreButton);
            console.log('Modern load more button added and centered');
        }
        
        function showEndMessage() {
            const container = document.getElementById('conversation-list');
            
            // Remove existing end message
            const existingMessage = container.querySelector('.end-message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            // Create end message with Tailwind classes
            const endMessage = document.createElement('div');
            endMessage.className = 'end-message success text-center text-green-600 text-sm font-medium py-4 px-4 mx-4 my-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl shadow-sm max-w-80 mx-auto';
            endMessage.innerHTML = '🎉 You\'ve reached the end! All conversations loaded.';
            
            container.appendChild(endMessage);
            console.log('Modern end message added');
        }
        
        function clearCards() {
            const container = document.getElementById('conversation-list');
            container.innerHTML = `
                <div class="empty-message flex flex-col items-center justify-center py-16 text-center">
                    <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-500 text-sm">No conversations recorded yet.</p>
                    <p class="text-gray-400 text-xs mt-1">Start chatting with AI assistants to see your conversations here.</p>
                </div>
            `;
            console.log('All cards cleared, showing modern empty state');
        }
        
        // Initialize with some cards
        document.addEventListener('DOMContentLoaded', () => {
            generateCards(8);
            showLoadMore();
            console.log('Tailwind CSS redesign test loaded successfully');
        });
    </script>
</body>
</html>
