/**
 * DOM Optimization Test Script
 * Tests virtual scrolling, template rendering, batch updates, and performance optimizations
 */

// Copy the DOMOptimizer class from popup.js
class DOMOptimizer {
  static createDocumentFragment() {
    return document.createDocumentFragment();
  }
  
  static batchDOMUpdates(callback) {
    // Use requestAnimationFrame for optimal timing
    return new Promise((resolve) => {
      requestAnimationFrame(() => {
        const result = callback();
        resolve(result);
      });
    });
  }
  
  static createElementFromTemplate(template, data = {}) {
    // Create a temporary container
    const container = document.createElement('div');
    container.innerHTML = template;
    
    // Replace placeholders with data
    const element = container.firstElementChild;
    if (element && data) {
      this.replacePlaceholders(element, data);
    }
    
    return element;
  }
  
  static replacePlaceholders(element, data) {
    // Replace text content placeholders
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );
    
    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node);
    }
    
    textNodes.forEach(textNode => {
      let content = textNode.textContent;
      Object.keys(data).forEach(key => {
        const placeholder = `{{${key}}}`;
        if (content.includes(placeholder)) {
          content = content.replace(new RegExp(placeholder, 'g'), data[key]);
        }
      });
      textNode.textContent = content;
    });
    
    // Replace attribute placeholders
    const allElements = element.querySelectorAll('*');
    allElements.forEach(el => {
      Array.from(el.attributes).forEach(attr => {
        let value = attr.value;
        Object.keys(data).forEach(key => {
          const placeholder = `{{${key}}}`;
          if (value.includes(placeholder)) {
            value = value.replace(new RegExp(placeholder, 'g'), data[key]);
          }
        });
        attr.value = value;
      });
    });
  }
  
  static measurePerformance(name, callback) {
    const startTime = performance.now();
    const result = callback();
    const endTime = performance.now();
    
    const duration = endTime - startTime;
    logPerformance(`${name} took ${duration.toFixed(2)}ms`, duration);
    return result;
  }
  
  static debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
  
  static throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
  
  static createOptimizedList(container, items, renderItem, options = {}) {
    const {
      batchSize = 50,
      delay = 0
    } = options;
    
    const fragment = this.createDocumentFragment();
    let currentIndex = 0;
    
    const renderBatch = () => {
      const endIndex = Math.min(currentIndex + batchSize, items.length);
      
      for (let i = currentIndex; i < endIndex; i++) {
        const element = renderItem(items[i], i);
        if (element) {
          fragment.appendChild(element);
        }
      }
      
      currentIndex = endIndex;
      
      if (currentIndex < items.length) {
        if (delay > 0) {
          setTimeout(renderBatch, delay);
        } else {
          requestAnimationFrame(renderBatch);
        }
      } else {
        // All items rendered, append to container
        container.appendChild(fragment);
      }
    };
    
    // Start rendering
    renderBatch();
  }
}

// Test templates
const OPTIMIZED_ITEM_TEMPLATE = `
  <div class="test-item optimized" data-id="{{id}}">
    <strong>{{title}}</strong>
    <p>{{description}}</p>
    <small>Created: {{date}}</small>
  </div>
`;

// Performance logging
function logPerformance(message, duration) {
  const log = document.getElementById('performance-log');
  const entry = document.createElement('div');
  entry.className = 'log-entry';
  
  // Color code based on performance
  if (duration < 10) {
    entry.classList.add('log-fast');
  } else if (duration < 50) {
    entry.classList.add('log-medium');
  } else {
    entry.classList.add('log-slow');
  }
  
  entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
  log.appendChild(entry);
  log.scrollTop = log.scrollHeight;
  
  console.log(message);
}

// Generate test data
function generateTestData(count) {
  const items = [];
  for (let i = 0; i < count; i++) {
    items.push({
      id: i + 1,
      title: `Test Item ${i + 1}`,
      description: `This is a test description for item ${i + 1}. It contains some sample text to test rendering performance.`,
      date: new Date(Date.now() - (i * 60000)).toLocaleString()
    });
  }
  return items;
}

// Optimized rendering test
function testOptimizedRendering(count) {
  const container = document.getElementById('optimized-content');
  const items = generateTestData(count);
  
  const time = DOMOptimizer.measurePerformance(`Optimized Rendering (${count} items)`, () => {
    container.innerHTML = '';
    
    const fragment = DOMOptimizer.createDocumentFragment();
    
    items.forEach(item => {
      const element = DOMOptimizer.createElementFromTemplate(OPTIMIZED_ITEM_TEMPLATE, item);
      if (element) {
        fragment.appendChild(element);
      }
    });
    
    container.appendChild(fragment);
  });
  
  optimizedTime = time;
  updateMetrics();
  domNodeCount = container.children.length;
}

// Unoptimized rendering test
function testUnoptimizedRendering(count) {
  const container = document.getElementById('unoptimized-content');
  const items = generateTestData(count);
  
  const startTime = performance.now();
  
  container.innerHTML = '';
  
  items.forEach(item => {
    const element = document.createElement('div');
    element.className = 'test-item unoptimized';
    element.setAttribute('data-id', item.id);
    element.innerHTML = `
      <strong>${escapeHTML(item.title)}</strong>
      <p>${escapeHTML(item.description)}</p>
      <small>Created: ${escapeHTML(item.date)}</small>
    `;
    container.appendChild(element); // Individual DOM append (inefficient)
  });
  
  const endTime = performance.now();
  unoptimizedTime = endTime - startTime;
  
  logPerformance(`Unoptimized Rendering (${count} items) took ${unoptimizedTime.toFixed(2)}ms`, unoptimizedTime);
  updateMetrics();
}

// Template vs manual rendering test
function testTemplateRendering() {
  const count = 200;
  const items = generateTestData(count);
  
  // Template rendering
  const templateTime = DOMOptimizer.measurePerformance('Template Rendering', () => {
    const fragment = DOMOptimizer.createDocumentFragment();
    items.forEach(item => {
      const element = DOMOptimizer.createElementFromTemplate(OPTIMIZED_ITEM_TEMPLATE, item);
      if (element) {
        fragment.appendChild(element);
      }
    });
  });
  
  // Manual rendering
  const manualTime = DOMOptimizer.measurePerformance('Manual Rendering', () => {
    const fragment = DOMOptimizer.createDocumentFragment();
    items.forEach(item => {
      const element = document.createElement('div');
      element.className = 'test-item';
      element.innerHTML = `
        <strong>${escapeHTML(item.title)}</strong>
        <p>${escapeHTML(item.description)}</p>
        <small>Created: ${escapeHTML(item.date)}</small>
      `;
      fragment.appendChild(element);
    });
  });
  
  const improvement = (manualTime / templateTime).toFixed(2);
  logPerformance(`Template rendering is ${improvement}x faster than manual`, 0);
}

// Batch vs individual updates test
function testBatchUpdates() {
  const count = 100;
  const container = document.getElementById('optimized-content');
  
  // Individual updates
  const individualTime = DOMOptimizer.measurePerformance('Individual Updates', () => {
    container.innerHTML = '';
    for (let i = 0; i < count; i++) {
      const element = document.createElement('div');
      element.textContent = `Item ${i + 1}`;
      container.appendChild(element); // Individual append
    }
  });
  
  // Batch updates
  const batchTime = DOMOptimizer.measurePerformance('Batch Updates', () => {
    container.innerHTML = '';
    const fragment = DOMOptimizer.createDocumentFragment();
    for (let i = 0; i < count; i++) {
      const element = document.createElement('div');
      element.textContent = `Item ${i + 1}`;
      fragment.appendChild(element);
    }
    container.appendChild(fragment); // Single append
  });
  
  const improvement = (individualTime / batchTime).toFixed(2);
  logPerformance(`Batch updates are ${improvement}x faster than individual`, 0);
}

// Document fragment test
function testDocumentFragment() {
  const count = 150;
  const container = document.getElementById('unoptimized-content');
  
  // Without fragment
  const directTime = DOMOptimizer.measurePerformance('Direct DOM Manipulation', () => {
    container.innerHTML = '';
    for (let i = 0; i < count; i++) {
      const element = document.createElement('div');
      element.textContent = `Direct Item ${i + 1}`;
      container.appendChild(element);
    }
  });
  
  // With fragment
  const fragmentTime = DOMOptimizer.measurePerformance('Document Fragment', () => {
    container.innerHTML = '';
    const fragment = DOMOptimizer.createDocumentFragment();
    for (let i = 0; i < count; i++) {
      const element = document.createElement('div');
      element.textContent = `Fragment Item ${i + 1}`;
      fragment.appendChild(element);
    }
    container.appendChild(fragment);
  });
  
  const improvement = (directTime / fragmentTime).toFixed(2);
  logPerformance(`Document fragments are ${improvement}x faster`, 0);
}

// Debouncing test
function testDebouncing() {
  let callCount = 0;
  const debouncedFunction = DOMOptimizer.debounce(() => {
    callCount++;
    logPerformance(`Debounced function called (total calls: ${callCount})`, 0);
  }, 300);
  
  // Simulate rapid calls
  for (let i = 0; i < 10; i++) {
    setTimeout(() => debouncedFunction(), i * 50);
  }
  
  logPerformance('Debouncing test started - should only call function once', 0);
}

// Throttling test
function testThrottling() {
  let callCount = 0;
  const throttledFunction = DOMOptimizer.throttle(() => {
    callCount++;
    logPerformance(`Throttled function called (total calls: ${callCount})`, 0);
  }, 200);
  
  // Simulate rapid calls
  for (let i = 0; i < 20; i++) {
    setTimeout(() => throttledFunction(), i * 50);
  }
  
  logPerformance('Throttling test started - should limit function calls', 0);
}

// Clear all tests
function clearTests() {
  document.getElementById('optimized-content').innerHTML = '';
  document.getElementById('unoptimized-content').innerHTML = '';
  document.getElementById('performance-log').innerHTML = '<div class="log-entry">Performance log cleared...</div>';
  
  optimizedTime = 0;
  unoptimizedTime = 0;
  domNodeCount = 0;
  updateMetrics();
}

// Update performance metrics display
function updateMetrics() {
  document.getElementById('optimized-time').textContent = `${optimizedTime.toFixed(2)}ms`;
  document.getElementById('unoptimized-time').textContent = `${unoptimizedTime.toFixed(2)}ms`;
  
  if (optimizedTime > 0 && unoptimizedTime > 0) {
    const ratio = (unoptimizedTime / optimizedTime).toFixed(2);
    document.getElementById('performance-ratio').textContent = `${ratio}x`;
  } else {
    document.getElementById('performance-ratio').textContent = '0x';
  }
  
  document.getElementById('dom-nodes').textContent = domNodeCount;
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
  logPerformance('DOM optimization test page loaded', 0);
  updateMetrics();
  
  console.log('DOM optimization test loaded. Use the buttons to test different scenarios.');
});
