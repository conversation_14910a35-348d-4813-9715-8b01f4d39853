<!DOCTYPE html>
<html lang="en">
<head>
    <title>Error Handling & User Feedback Test - LLMLog</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../popup.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: #4a90e2;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .control-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .test-button {
            padding: 10px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            background: #f0f0f0;
            border-color: #bbb;
        }
        
        .test-button.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .test-button.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .test-button.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .test-button.info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .test-demo {
            padding: 20px;
            min-height: 200px;
            border-bottom: 1px solid #eee;
        }
        
        .demo-item {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #f9f9f9;
        }
        
        .demo-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .demo-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .loading-demo {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .test-results {
            padding: 20px;
            background: #f8f9fa;
        }
        
        .result-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .result-success {
            background: #d4edda;
            color: #155724;
        }
        
        .result-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .result-info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>Error Handling & User Feedback Test</h1>
            <p>Testing toast notifications, loading states, confirmation dialogs, and error handling</p>
        </div>
        
        <div class="test-controls">
            <div class="control-group">
                <h3>Toast Notifications</h3>
                <div class="control-buttons">
                    <button class="test-button success" onclick="testSuccessToast()">Success Toast</button>
                    <button class="test-button error" onclick="testErrorToast()">Error Toast</button>
                    <button class="test-button warning" onclick="testWarningToast()">Warning Toast</button>
                    <button class="test-button info" onclick="testInfoToast()">Info Toast</button>
                    <button class="test-button" onclick="testLongToast()">Long Message</button>
                    <button class="test-button" onclick="testMultipleToasts()">Multiple Toasts</button>
                </div>
            </div>
            
            <div class="control-group">
                <h3>Loading States</h3>
                <div class="control-buttons">
                    <button class="test-button" onclick="testButtonLoading(this)">Button Loading</button>
                    <button class="test-button" onclick="testElementLoading()">Element Loading</button>
                    <button class="test-button" onclick="testCustomLoading()">Custom Message</button>
                </div>
            </div>
            
            <div class="control-group">
                <h3>Confirmation Dialogs</h3>
                <div class="control-buttons">
                    <button class="test-button" onclick="testConfirmDialog()">Basic Confirm</button>
                    <button class="test-button" onclick="testCustomConfirm()">Custom Confirm</button>
                    <button class="test-button" onclick="testDeleteConfirm()">Delete Confirm</button>
                </div>
            </div>
            
            <div class="control-group">
                <h3>Error Scenarios</h3>
                <div class="control-buttons">
                    <button class="test-button" onclick="testNetworkError()">Network Error</button>
                    <button class="test-button" onclick="testValidationError()">Validation Error</button>
                    <button class="test-button" onclick="testUnknownError()">Unknown Error</button>
                    <button class="test-button" onclick="testErrorWithContext()">Error with Context</button>
                </div>
            </div>
        </div>
        
        <div class="test-demo">
            <h2>Demo Area</h2>
            <div class="demo-item">
                <h4>Loading State Demo</h4>
                <p>Click the buttons above to see loading states applied to this element.</p>
                <div id="demo-element" style="padding: 20px; background: #e9ecef; border-radius: 4px; margin-top: 10px;">
                    This element can show loading states
                </div>
            </div>
            
            <div class="demo-item">
                <h4>Interactive Elements</h4>
                <p>These elements will show loading states when clicked:</p>
                <button class="loading-demo" onclick="simulateAsyncOperation(this, 'Saving data...')">Save Data</button>
                <button class="loading-demo" onclick="simulateAsyncOperation(this, 'Loading...')">Load Content</button>
                <button class="loading-demo" onclick="simulateAsyncOperation(this, 'Processing...')">Process</button>
            </div>
        </div>
        
        <div class="test-results">
            <h2>Test Results</h2>
            <div id="test-log">
                <div class="result-info">Test log will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Include required libraries -->
    <script src="../lib/dompurify.min.js"></script>
    
    <script>
        // Helper function to escape HTML
        function escapeHTML(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Mock global variables
        let allConversations = [];
        let accessibilityManager = null;
    </script>
    
    <script src="test-error-handling.js"></script>
</body>
</html>
