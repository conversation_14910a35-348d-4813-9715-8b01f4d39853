/**
 * Claude Duplicate Detection Fix Test Script
 * 
 * This script specifically tests Claude conversation duplicate detection
 * to ensure conversations are not saved multiple times.
 * 
 * Usage:
 * 1. Open a Claude conversation page
 * 2. Open browser developer tools (F12)
 * 3. Copy and paste this script into the console
 * 4. Follow the test instructions
 */

console.log('=== Claude Duplicate Detection Fix Test ===');
console.log('Testing Claude-specific duplicate detection...\n');

// Test state tracking
let conversationCount = 0;
let duplicateCount = 0;
let conversationUpdateCount = 0;
let conversationLog = [];
let testStartTime = Date.now();

// Monitor both LLMLOG_CONVERSATION_UPDATE and LLMLOG_CONVERSATION messages
const originalPostMessage = window.postMessage;
window.postMessage = function(message, targetOrigin) {
    if (message.type === 'LLMLOG_CONVERSATION_UPDATE') {
        conversationUpdateCount++;
        const payload = message.payload;
        
        console.log(`📤 Claude Conversation Update #${conversationUpdateCount}:`, {
            platform: payload.platform,
            promptPreview: payload.prompt.substring(0, 50) + '...',
            responsePreview: payload.response.substring(0, 50) + '...',
            timestamp: payload.createdAt,
            url: payload.url
        });
    }
    
    if (message.type === 'LLMLOG_CONVERSATION') {
        conversationCount++;
        const payload = message.payload;
        
        console.log(`📨 Final Conversation Captured #${conversationCount}:`, {
            platform: payload.platform,
            promptPreview: payload.prompt.substring(0, 50) + '...',
            responsePreview: payload.response.substring(0, 50) + '...',
            timestamp: payload.createdAt,
            timeSinceTest: Math.round((Date.now() - testStartTime) / 1000) + 's'
        });
        
        // Check for duplicates in our local log
        const isDuplicate = conversationLog.some(conv => 
            conv.prompt === payload.prompt && 
            conv.response === payload.response && 
            conv.platform === payload.platform
        );
        
        if (isDuplicate) {
            duplicateCount++;
            console.warn(`⚠️ DUPLICATE DETECTED #${duplicateCount}!`, {
                type: 'Same content in final conversation log',
                platform: payload.platform
            });
        } else {
            console.log('✅ New unique conversation in final log');
        }
        
        conversationLog.push({
            platform: payload.platform,
            url: payload.url,
            prompt: payload.prompt,
            response: payload.response,
            timestamp: payload.createdAt,
            captureNumber: conversationCount,
            testTime: Date.now() - testStartTime
        });
    }
    
    return originalPostMessage.call(this, message, targetOrigin);
};

// Test functions
function testClaudeConversationFlow() {
    console.log('\n🧪 Testing Claude Conversation Flow...');
    console.log('💡 Instructions:');
    console.log('1. Type a prompt in the Claude interface');
    console.log('2. Send the message and wait for response');
    console.log('3. Check the console logs above');
    console.log('4. Verify only ONE final conversation is captured');
    
    console.log('\n📊 Current State:');
    console.log(`   Updates sent: ${conversationUpdateCount}`);
    console.log(`   Final conversations: ${conversationCount}`);
    console.log(`   Duplicates detected: ${duplicateCount}`);
    
    console.log('\n⏱️ Monitoring for 60 seconds...');
    
    const initialUpdateCount = conversationUpdateCount;
    const initialConversationCount = conversationCount;
    
    setTimeout(() => {
        const newUpdates = conversationUpdateCount - initialUpdateCount;
        const newConversations = conversationCount - initialConversationCount;
        
        console.log(`\n📊 Test Results (60 seconds):`, {
            conversationUpdates: newUpdates,
            finalConversations: newConversations,
            duplicatesDetected: duplicateCount,
            ratio: newUpdates > 0 ? `${newConversations}/${newUpdates}` : 'N/A',
            status: newConversations === 1 && newUpdates >= 1 ? 
                '✅ PASS - Single conversation from updates' : 
                newConversations === 0 ? 
                '❌ FAIL - No conversations captured' :
                newConversations > 1 ?
                '❌ FAIL - Multiple conversations captured' :
                '⚠️ UNKNOWN - Check logs'
        });
        
        if (newConversations === 0) {
            console.log('\n❌ No conversations captured!');
            console.log('💡 Possible issues:');
            console.log('   1. Claude API endpoint changed');
            console.log('   2. Duplicate detection too aggressive');
            console.log('   3. Platform module not loading');
        } else if (newConversations > 1) {
            console.log('\n❌ Multiple conversations captured for single interaction!');
            console.log('💡 This indicates duplicate detection is not working properly');
        } else {
            console.log('\n✅ Perfect! Single conversation captured as expected');
        }
    }, 60000);
}

function testClaudeDuplicateDetection() {
    console.log('\n🔒 Testing Claude Duplicate Detection Layers...');
    
    // Test platform-level duplicate detection
    console.log('\n📋 Testing platform-level duplicate detection...');
    
    // Simulate Claude conversation update
    const testPayload = {
        platform: 'Claude',
        prompt: 'Test prompt for Claude duplicate detection - ' + Date.now(),
        response: 'Test response for Claude duplicate detection - ' + Date.now(),
        url: window.location.href,
        createdAt: new Date().toISOString(),
        title: 'Test conversation'
    };
    
    console.log('📤 Sending first Claude conversation update...');
    window.postMessage({
        type: 'LLMLOG_CONVERSATION_UPDATE',
        payload: testPayload
    }, window.location.origin);
    
    // Send duplicate after 2 seconds
    setTimeout(() => {
        console.log('📤 Sending duplicate Claude conversation update...');
        window.postMessage({
            type: 'LLMLOG_CONVERSATION_UPDATE',
            payload: {
                ...testPayload,
                createdAt: new Date().toISOString() // Different timestamp but same content
            }
        }, window.location.origin);
        
        // Check results after 3 seconds
        setTimeout(() => {
            const testConversations = conversationLog.filter(conv => 
                conv.prompt.includes('Test prompt for Claude duplicate detection')
            );
            
            console.log(`\n📊 Claude Duplicate Test Results:`, {
                testUpdatesSent: 2,
                finalConversationsCaptured: testConversations.length,
                duplicatesBlocked: 2 - testConversations.length,
                status: testConversations.length === 1 ? 
                    '✅ PASS - Duplicate blocked correctly' : 
                    testConversations.length === 0 ?
                    '❌ FAIL - All conversations blocked' :
                    '❌ FAIL - Duplicate not blocked'
            });
        }, 3000);
    }, 2000);
}

function checkClaudeAPIEndpoint() {
    console.log('\n🔍 Checking Claude API Endpoint Detection...');
    
    // Check if we're on Claude
    const isClaudePage = window.location.hostname === 'claude.ai';
    console.log(`🌐 On Claude page: ${isClaudePage}`);
    
    if (isClaudePage) {
        // Check current URL pattern
        const currentUrl = window.location.href;
        const claudeConversationMatch = currentUrl.match(/\/chat\/([a-f0-9-]+)/);
        
        console.log(`📍 Current URL: ${currentUrl}`);
        console.log(`🔗 Conversation ID: ${claudeConversationMatch ? claudeConversationMatch[1] : 'Not found'}`);
        
        // Test API endpoint regex
        const apiEndpointRegex = /^\/api\/organizations\/[a-f0-9-]+\/chat_conversations\/[a-f0-9-]+$/;
        const testEndpoints = [
            '/api/organizations/12345678-1234-1234-1234-123456789abc/chat_conversations/87654321-4321-4321-4321-cba987654321',
            '/api/organizations/invalid/chat_conversations/invalid',
            '/api/other/endpoint'
        ];
        
        console.log('\n🧪 Testing API endpoint regex:');
        testEndpoints.forEach((endpoint, index) => {
            const matches = apiEndpointRegex.test(endpoint);
            console.log(`   ${index + 1}. ${endpoint} → ${matches ? '✅ Match' : '❌ No match'}`);
        });
    } else {
        console.log('⚠️ Not on Claude page - some tests may not work');
    }
}

function monitorClaudeNetworkActivity() {
    console.log('\n📡 Monitoring Claude Network Activity...');
    console.log('💡 This will show you what API calls Claude is making');
    
    // Override fetch to monitor Claude API calls
    const originalFetch = window.fetch;
    let apiCallCount = 0;
    
    window.fetch = async function(...args) {
        const url = args[0] instanceof Request ? args[0].url : args[0];
        const requestUrl = new URL(url, window.location.origin);
        
        // Check if this matches Claude API pattern
        const apiEndpointRegex = /^\/api\/organizations\/[a-f0-9-]+\/chat_conversations\/[a-f0-9-]+$/;
        const isClaudeAPI = apiEndpointRegex.test(requestUrl.pathname);
        
        if (isClaudeAPI) {
            apiCallCount++;
            console.log(`🌐 Claude API Call #${apiCallCount}:`, {
                method: args[1]?.method || 'GET',
                pathname: requestUrl.pathname,
                timestamp: new Date().toISOString()
            });
        }
        
        return originalFetch.apply(this, args);
    };
    
    console.log('✅ Network monitoring enabled');
    console.log('   Send a message in Claude to see API calls');
    
    // Restore original fetch after 2 minutes
    setTimeout(() => {
        window.fetch = originalFetch;
        console.log(`📊 Network monitoring stopped. Total Claude API calls: ${apiCallCount}`);
    }, 120000);
}

function getClaudeTestSummary() {
    console.log('\n📊 Claude Test Summary:');
    console.log('======================');
    console.log(`📤 Conversation updates sent: ${conversationUpdateCount}`);
    console.log(`📨 Final conversations captured: ${conversationCount}`);
    console.log(`⚠️ Duplicates detected: ${duplicateCount}`);
    console.log(`⏱️ Test duration: ${Math.round((Date.now() - testStartTime) / 1000)} seconds`);
    
    const ratio = conversationUpdateCount > 0 ? (conversationCount / conversationUpdateCount) : 0;
    console.log(`📈 Conversion ratio: ${ratio.toFixed(2)} (final conversations / updates)`);
    
    console.log('\n🎯 Analysis:');
    if (conversationCount === 0 && conversationUpdateCount === 0) {
        console.log('❌ CRITICAL: No activity detected - check if extension is working');
    } else if (conversationCount === 0 && conversationUpdateCount > 0) {
        console.log('❌ CRITICAL: Updates sent but no final conversations - duplicate detection too aggressive');
    } else if (conversationCount > conversationUpdateCount) {
        console.log('❌ ERROR: More final conversations than updates - logic error');
    } else if (conversationCount === conversationUpdateCount && conversationCount > 1) {
        console.log('⚠️ WARNING: All updates become final conversations - no duplicate detection');
    } else if (ratio >= 0.5 && ratio <= 1.0) {
        console.log('✅ GOOD: Reasonable conversion ratio - duplicate detection working');
    } else {
        console.log('⚠️ REVIEW: Unusual ratio - check logs for issues');
    }
    
    return {
        conversationUpdates: conversationUpdateCount,
        finalConversations: conversationCount,
        duplicatesDetected: duplicateCount,
        conversionRatio: ratio,
        testDuration: Math.round((Date.now() - testStartTime) / 1000),
        conversationLog: conversationLog
    };
}

// Export test functions
window.claudeTest = {
    testFlow: testClaudeConversationFlow,
    testDuplicates: testClaudeDuplicateDetection,
    checkEndpoint: checkClaudeAPIEndpoint,
    monitorNetwork: monitorClaudeNetworkActivity,
    summary: getClaudeTestSummary,
    getLog: () => conversationLog,
    getStats: () => ({
        updates: conversationUpdateCount,
        conversations: conversationCount,
        duplicates: duplicateCount,
        ratio: conversationUpdateCount > 0 ? (conversationCount / conversationUpdateCount) : 0
    })
};

console.log('\n🚀 Claude Test Suite Loaded!');
console.log('📝 Available commands:');
console.log('   claudeTest.testFlow() - Test Claude conversation flow');
console.log('   claudeTest.testDuplicates() - Test duplicate detection');
console.log('   claudeTest.checkEndpoint() - Check API endpoint detection');
console.log('   claudeTest.monitorNetwork() - Monitor Claude API calls');
console.log('   claudeTest.summary() - Get detailed summary');

console.log('\n💡 Quick Test Instructions:');
console.log('1. Run claudeTest.testFlow() and send a message in Claude');
console.log('2. Check that only ONE final conversation is captured');
console.log('3. Run claudeTest.testDuplicates() to verify duplicate prevention');

// Auto-start basic checks
console.log('\n⏳ Running initial checks...');
setTimeout(() => {
    checkClaudeAPIEndpoint();
    testClaudeConversationFlow();
}, 1000);
