/**
 * Duplicate Detection Fix Test Script
 * 
 * This script tests the improved duplicate detection system to ensure
 * that reloading conversation pages doesn't create duplicate entries.
 * 
 * Usage:
 * 1. Open a Claude conversation page
 * 2. Open browser developer tools (F12)
 * 3. Copy and paste this script into the console
 * 4. Follow the test instructions
 */

console.log('=== LLMLog Duplicate Detection Fix Test ===');
console.log('Testing improved duplicate detection for page reloads...\n');

// Test state tracking
let testResults = [];
let conversationCount = 0;
let duplicateCount = 0;
let conversationLog = [];

// Monitor conversation captures
const originalPostMessage = window.postMessage;
window.postMessage = function(message, targetOrigin) {
    if (message.type === 'LLMLOG_CONVERSATION') {
        conversationCount++;
        const payload = message.payload;
        
        console.log(`📨 Conversation Captured #${conversationCount}:`, {
            platform: payload.platform,
            url: payload.url,
            prompt: payload.prompt.substring(0, 50) + '...',
            response: payload.response.substring(0, 50) + '...',
            timestamp: payload.createdAt
        });
        
        // Check for duplicates in our local log
        const isDuplicate = conversationLog.some(conv => 
            (conv.url === payload.url && conv.url) ||
            (conv.prompt === payload.prompt && conv.response === payload.response && conv.platform === payload.platform)
        );
        
        if (isDuplicate) {
            duplicateCount++;
            console.warn(`⚠️ DUPLICATE DETECTED #${duplicateCount}!`, {
                type: 'Local duplicate check',
                url: payload.url,
                platform: payload.platform
            });
        } else {
            console.log('✅ New unique conversation detected');
        }
        
        conversationLog.push({
            platform: payload.platform,
            url: payload.url,
            prompt: payload.prompt,
            response: payload.response,
            timestamp: payload.createdAt,
            captureNumber: conversationCount
        });
    }
    
    return originalPostMessage.call(this, message, targetOrigin);
};

// Test functions
function testDuplicateDetection() {
    console.log('\n🧪 Starting Duplicate Detection Tests...');
    
    // Test 1: Simulate same conversation data
    console.log('\n📋 Test 1: Same conversation content');
    const testConversation = {
        platform: 'Claude',
        prompt: 'Test prompt for duplicate detection',
        response: 'Test response for duplicate detection',
        url: window.location.href,
        createdAt: new Date().toISOString(),
        title: 'Test prompt for duplicate detection'
    };
    
    // Send first conversation
    window.postMessage({
        type: 'LLMLOG_CONVERSATION',
        payload: testConversation
    }, window.location.origin);
    
    // Send duplicate after 1 second
    setTimeout(() => {
        console.log('📋 Sending duplicate conversation...');
        window.postMessage({
            type: 'LLMLOG_CONVERSATION',
            payload: {
                ...testConversation,
                createdAt: new Date().toISOString() // Different timestamp
            }
        }, window.location.origin);
    }, 1000);
    
    // Test 2: Same URL different content
    setTimeout(() => {
        console.log('\n📋 Test 2: Same URL, different content');
        window.postMessage({
            type: 'LLMLOG_CONVERSATION',
            payload: {
                ...testConversation,
                prompt: 'Different prompt',
                response: 'Different response',
                createdAt: new Date().toISOString()
            }
        }, window.location.origin);
    }, 2000);
}

function checkStoredConversations() {
    console.log('\n🔍 Checking stored conversations...');
    
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({
            namespace: 'database',
            action: 'getAllConversations'
        }, (response) => {
            if (response && response.status === 'success') {
                const conversations = response.data;
                const currentUrl = window.location.href;
                
                // Find conversations with current URL
                const urlMatches = conversations.filter(conv => conv.url === currentUrl);
                
                console.log(`📊 Storage Analysis:`, {
                    totalConversations: conversations.length,
                    currentUrlMatches: urlMatches.length,
                    currentUrl: currentUrl
                });
                
                if (urlMatches.length > 1) {
                    console.warn(`⚠️ Found ${urlMatches.length} conversations with same URL!`);
                    urlMatches.forEach((conv, index) => {
                        console.log(`   ${index + 1}. ID: ${conv.id}, Created: ${conv.createdAt}`);
                    });
                } else {
                    console.log('✅ No URL-based duplicates found in storage');
                }
            } else {
                console.error('❌ Failed to retrieve conversations:', response);
            }
        });
    } else {
        console.warn('⚠️ Chrome extension API not available');
    }
}

function simulatePageReload() {
    console.log('\n🔄 Simulating page reload scenario...');
    console.log('💡 To test page reload duplicates:');
    console.log('   1. Make sure you have a conversation on this page');
    console.log('   2. Note the conversation count above');
    console.log('   3. Reload the page (F5 or Ctrl+R)');
    console.log('   4. Run this script again');
    console.log('   5. Check if the same conversation is captured again');
    
    checkStoredConversations();
}

function clearTestData() {
    console.log('\n🧹 Clearing test data...');
    conversationCount = 0;
    duplicateCount = 0;
    conversationLog = [];
    testResults = [];
    console.log('✅ Test data cleared');
}

function getTestSummary() {
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log(`📨 Total conversations captured: ${conversationCount}`);
    console.log(`⚠️ Duplicates detected: ${duplicateCount}`);
    console.log(`✅ Unique conversations: ${conversationCount - duplicateCount}`);
    
    if (duplicateCount > 0) {
        console.log('\n⚠️ Duplicate detection is working - this is good!');
        console.log('   The system successfully prevented duplicate saves.');
    } else if (conversationCount > 1) {
        console.log('\n❌ No duplicates detected - this might indicate an issue');
        console.log('   Multiple conversations were captured without duplicate detection.');
    } else {
        console.log('\n💡 Run more tests to verify duplicate detection');
    }
    
    return {
        totalConversations: conversationCount,
        duplicatesDetected: duplicateCount,
        uniqueConversations: conversationCount - duplicateCount,
        conversationLog: conversationLog
    };
}

// Enhanced duplicate detection test
function testEnhancedDuplicateDetection() {
    console.log('\n🔬 Testing Enhanced Duplicate Detection Features...');
    
    // Test conversation ID extraction
    const testUrls = [
        'https://chat.openai.com/c/12345678-1234-1234-1234-123456789abc',
        'https://claude.ai/chat/87654321-4321-4321-4321-cba987654321',
        'https://gemini.google.com/app/abcdef12-3456-7890-abcd-ef1234567890'
    ];
    
    testUrls.forEach((url, index) => {
        console.log(`🔗 Test URL ${index + 1}: ${url}`);
        
        // Extract conversation ID using the same logic as the interceptor
        let conversationId = null;
        const chatgptMatch = url.match(/\/c\/([a-f0-9-]+)/);
        const claudeMatch = url.match(/\/chat\/([a-f0-9-]+)/);
        const geminiMatch = url.match(/\/app\/([a-f0-9-]+)/);
        
        conversationId = chatgptMatch?.[1] || claudeMatch?.[1] || geminiMatch?.[1];
        
        console.log(`   Extracted ID: ${conversationId || 'None'}`);
    });
}

// Export test functions
window.duplicateDetectionTest = {
    test: testDuplicateDetection,
    checkStorage: checkStoredConversations,
    simulateReload: simulatePageReload,
    clear: clearTestData,
    summary: getTestSummary,
    enhanced: testEnhancedDuplicateDetection,
    getLog: () => conversationLog,
    getStats: () => ({
        conversations: conversationCount,
        duplicates: duplicateCount,
        unique: conversationCount - duplicateCount
    })
};

console.log('\n🚀 Duplicate Detection Test Suite Loaded!');
console.log('📝 Available commands:');
console.log('   duplicateDetectionTest.test() - Run basic duplicate tests');
console.log('   duplicateDetectionTest.checkStorage() - Check stored conversations');
console.log('   duplicateDetectionTest.simulateReload() - Test page reload scenario');
console.log('   duplicateDetectionTest.enhanced() - Test enhanced features');
console.log('   duplicateDetectionTest.summary() - Get test summary');
console.log('   duplicateDetectionTest.clear() - Clear test data');

console.log('\n💡 Quick Test Instructions:');
console.log('1. Run duplicateDetectionTest.test() to test duplicate detection');
console.log('2. Run duplicateDetectionTest.checkStorage() to check for existing duplicates');
console.log('3. Reload the page and run this script again to test page reload duplicates');

// Auto-run basic checks
console.log('\n⏳ Running initial checks...');
setTimeout(() => {
    checkStoredConversations();
    testEnhancedDuplicateDetection();
}, 1000);
