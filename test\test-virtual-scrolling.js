/**
 * Virtual Scrolling Test Script
 * Tests the virtual scrolling implementation with various dataset sizes
 */

// Copy the VirtualScrollList class from popup.js
class VirtualScrollList {
  constructor(container, itemHeight = 80, bufferSize = 5) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.bufferSize = bufferSize;
    this.items = [];
    this.visibleStart = 0;
    this.visibleEnd = 0;
    this.scrollTop = 0;
    this.containerHeight = 0;
    this.totalHeight = 0;
    
    this.viewport = null;
    this.spacerTop = null;
    this.spacerBottom = null;
    
    this.init();
  }
  
  init() {
    // Clear existing content
    this.container.innerHTML = '';
    
    // Create virtual scroll structure
    this.viewport = document.createElement('div');
    this.viewport.className = 'virtual-scroll-viewport';
    this.viewport.style.cssText = `
      height: 100%;
      overflow-y: auto;
      position: relative;
    `;
    
    this.spacerTop = document.createElement('div');
    this.spacerTop.className = 'virtual-scroll-spacer-top';
    
    this.content = document.createElement('div');
    this.content.className = 'virtual-scroll-content';
    
    this.spacerBottom = document.createElement('div');
    this.spacerBottom.className = 'virtual-scroll-spacer-bottom';
    
    this.viewport.appendChild(this.spacerTop);
    this.viewport.appendChild(this.content);
    this.viewport.appendChild(this.spacerBottom);
    this.container.appendChild(this.viewport);
    
    // Add scroll listener
    this.viewport.addEventListener('scroll', () => {
      this.handleScroll();
    });
    
    // Update container height
    this.updateContainerHeight();
  }
  
  updateContainerHeight() {
    this.containerHeight = this.viewport.clientHeight;
  }
  
  setItems(items) {
    this.items = items;
    this.totalHeight = items.length * this.itemHeight;
    this.updateVisibleRange();
    this.render();
  }
  
  handleScroll() {
    this.scrollTop = this.viewport.scrollTop;
    this.updateVisibleRange();
    this.render();
    
    // Update performance metrics
    updatePerformanceMetrics();
  }
  
  updateVisibleRange() {
    if (this.items.length === 0) {
      this.visibleStart = 0;
      this.visibleEnd = 0;
      return;
    }
    
    const visibleItemCount = Math.ceil(this.containerHeight / this.itemHeight);
    this.visibleStart = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.bufferSize);
    this.visibleEnd = Math.min(this.items.length, this.visibleStart + visibleItemCount + (this.bufferSize * 2));
  }
  
  render() {
    const startTime = performance.now();
    
    // Update spacer heights
    this.spacerTop.style.height = `${this.visibleStart * this.itemHeight}px`;
    this.spacerBottom.style.height = `${(this.items.length - this.visibleEnd) * this.itemHeight}px`;
    
    // Clear content
    this.content.innerHTML = '';
    
    // Render visible items
    for (let i = this.visibleStart; i < this.visibleEnd; i++) {
      const item = this.items[i];
      if (item) {
        const element = this.createItemElement(item, i);
        this.content.appendChild(element);
      }
    }
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    // Update metrics
    document.getElementById('render-time').textContent = `${renderTime.toFixed(2)}ms`;
    document.getElementById('visible-count').textContent = `${this.visibleEnd - this.visibleStart}`;
    document.getElementById('dom-nodes').textContent = this.content.children.length;
  }
  
  createItemElement(conversation, index) {
    const item = document.createElement('div');
    item.className = 'conversation-item virtual-item';
    item.setAttribute('data-conversation-id', conversation.id);
    item.setAttribute('data-index', index);
    item.style.height = `${this.itemHeight}px`;
    item.innerHTML = `
      <div class="item-header">
        <span class="platform-badge ${conversation.platform.toLowerCase()}">${conversation.platform}</span>
        <span class="item-title">${escapeHTML(conversation.title)}</span>
        <span class="item-date">${new Date(conversation.createdAt).toLocaleString()}</span>
      </div>
      <div class="item-preview">
        <strong>You:</strong> ${escapeHTML(conversation.prompt.substring(0, 100))}...
      </div>
    `;
    
    item.addEventListener('click', () => {
      showDetailView(conversation);
    });

    const menuButton = document.createElement('button');
    menuButton.className = 'menu-button';
    menuButton.innerHTML = '...';
    menuButton.setAttribute('aria-label', 'More options');
    menuButton.addEventListener('click', (e) => {
      e.stopPropagation();
      if (confirm(`Are you sure you want to delete this conversation?\n\n"${conversation.prompt.substring(0, 50)}..."`)) {
        deleteConversation(conversation.id);
      }
    });

    item.querySelector('.item-header').appendChild(menuButton);
    return item;
  }
  
  scrollToTop() {
    this.viewport.scrollTop = 0;
  }
  
  destroy() {
    if (this.viewport) {
      this.viewport.removeEventListener('scroll', this.handleScroll);
    }
  }
}

// Test functions
function generateTestData(count) {
  const startTime = performance.now();
  document.getElementById('test-status').textContent = `Generating ${count} items...`;
  
  const platforms = ['ChatGPT', 'Gemini', 'Claude'];
  const samplePrompts = [
    'Explain quantum computing in simple terms',
    'Write a Python function to sort an array',
    'What are the benefits of renewable energy?',
    'How does machine learning work?',
    'Create a recipe for chocolate chip cookies',
    'Explain the theory of relativity',
    'Write a short story about time travel',
    'What is the meaning of life?',
    'How to learn a new programming language?',
    'Describe the process of photosynthesis'
  ];
  
  allConversations = [];
  
  for (let i = 0; i < count; i++) {
    const platform = platforms[i % platforms.length];
    const prompt = samplePrompts[i % samplePrompts.length];
    
    allConversations.push({
      id: i + 1,
      platform: platform,
      title: `Conversation ${i + 1}`,
      prompt: `${prompt} - Item ${i + 1}`,
      response: `This is a sample response for conversation ${i + 1}. It contains some detailed information about the topic.`,
      url: `https://example.com/conversation/${i + 1}`,
      createdAt: new Date(Date.now() - (i * 60000)).toISOString()
    });
  }
  
  // Initialize virtual scrolling
  const listElement = document.getElementById('conversation-list');
  if (virtualScrollList) {
    virtualScrollList.destroy();
  }
  
  virtualScrollList = new VirtualScrollList(listElement, 80, 5);
  virtualScrollList.setItems(allConversations);
  
  const endTime = performance.now();
  const generationTime = endTime - startTime;
  
  document.getElementById('test-status').textContent = `Generated ${count} items in ${generationTime.toFixed(2)}ms`;
  document.getElementById('item-count').textContent = count;
  
  updatePerformanceMetrics();
}

function clearData() {
  allConversations = [];
  if (virtualScrollList) {
    virtualScrollList.destroy();
    virtualScrollList = null;
  }
  
  const listElement = document.getElementById('conversation-list');
  listElement.innerHTML = '<p class="empty-message">Click a button above to generate test data</p>';
  
  document.getElementById('test-status').textContent = 'Ready';
  document.getElementById('item-count').textContent = '0';
  document.getElementById('visible-count').textContent = '0';
  document.getElementById('render-time').textContent = '0ms';
  document.getElementById('dom-nodes').textContent = '0';
}

function scrollToTop() {
  if (virtualScrollList) {
    virtualScrollList.scrollToTop();
  }
}

function updatePerformanceMetrics() {
  // Update memory usage if available
  if (performance.memory) {
    const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
    document.getElementById('memory-usage').textContent = `${memoryMB}MB`;
  }
  
  // Simple scroll performance indicator
  const domNodes = virtualScrollList ? virtualScrollList.content.children.length : 0;
  const totalItems = allConversations.length;
  
  let scrollPerformance = 'Good';
  if (domNodes > 100) {
    scrollPerformance = 'Fair';
  }
  if (domNodes > 200) {
    scrollPerformance = 'Poor';
  }
  
  document.getElementById('scroll-performance').textContent = scrollPerformance;
}

// Performance monitoring
let scrollEventCount = 0;
let lastScrollTime = 0;

document.addEventListener('scroll', () => {
  scrollEventCount++;
  lastScrollTime = performance.now();
}, true);

// Log performance stats every 5 seconds
setInterval(() => {
  if (virtualScrollList && allConversations.length > 0) {
    console.log('Performance Stats:', {
      totalItems: allConversations.length,
      visibleItems: virtualScrollList.visibleEnd - virtualScrollList.visibleStart,
      domNodes: virtualScrollList.content.children.length,
      scrollEvents: scrollEventCount,
      memoryUsage: performance.memory ? `${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB` : 'N/A'
    });
    scrollEventCount = 0;
  }
}, 5000);

console.log('Virtual Scrolling Test loaded. Use the buttons to test with different dataset sizes.');
