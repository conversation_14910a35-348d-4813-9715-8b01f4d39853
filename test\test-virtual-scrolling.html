<!DOCTYPE html>
<html>
<head>
    <title>Virtual Scrolling Test - LLMLog</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="../popup.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: #4a90e2;
            color: white;
            padding: 15px;
            text-align: center;
        }
        
        .test-controls {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .test-controls button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        
        .test-controls button:hover {
            background: #f0f0f0;
        }
        
        .test-popup {
            height: 500px;
            position: relative;
        }
        
        #conversation-list {
            height: 100%;
        }
        
        .test-info {
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #666;
        }
        
        .performance-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        
        .metric {
            background: white;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        
        .metric-value {
            font-weight: bold;
            color: #4a90e2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h2>Virtual Scrolling Test</h2>
            <p>Testing virtual scrolling performance with large datasets</p>
        </div>
        
        <div class="test-controls">
            <button onclick="generateTestData(100)">100 Items</button>
            <button onclick="generateTestData(500)">500 Items</button>
            <button onclick="generateTestData(1000)">1,000 Items</button>
            <button onclick="generateTestData(5000)">5,000 Items</button>
            <button onclick="clearData()">Clear</button>
            <button onclick="scrollToTop()">Scroll to Top</button>
        </div>
        
        <div class="test-popup">
            <div id="conversation-list">
                <p class="empty-message">Click a button above to generate test data</p>
            </div>
        </div>
        
        <div class="test-info">
            <div>
                <strong>Test Status:</strong> <span id="test-status">Ready</span>
            </div>
            <div>
                <strong>Items Generated:</strong> <span id="item-count">0</span>
            </div>
            <div>
                <strong>Visible Items:</strong> <span id="visible-count">0</span>
            </div>
            
            <div class="performance-metrics">
                <div class="metric">
                    <div>Render Time</div>
                    <div class="metric-value" id="render-time">0ms</div>
                </div>
                <div class="metric">
                    <div>Memory Usage</div>
                    <div class="metric-value" id="memory-usage">N/A</div>
                </div>
                <div class="metric">
                    <div>Scroll Performance</div>
                    <div class="metric-value" id="scroll-performance">Good</div>
                </div>
                <div class="metric">
                    <div>DOM Nodes</div>
                    <div class="metric-value" id="dom-nodes">0</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mock the required global variables and functions
        let allConversations = [];
        let virtualScrollList = null;
        let hasMorePages = false;
        let currentPage = 1;
        let currentView = 'list';
        
        // Mock chrome runtime for testing
        window.chrome = {
            runtime: {
                sendMessage: function(message, callback) {
                    // Mock response
                    setTimeout(() => {
                        callback({ status: 'success', data: [] });
                    }, 100);
                }
            }
        };
        
        // Mock DOMPurify for testing
        window.DOMPurify = {
            sanitize: function(html) {
                return html;
            }
        };
        
        // Mock marked for testing
        window.marked = {
            parse: function(text) {
                return text;
            }
        };
        
        // Helper function to escape HTML
        function escapeHTML(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Mock functions
        function showDetailView(conversation) {
            console.log('Detail view for:', conversation.title);
        }
        
        function deleteConversation(id) {
            console.log('Delete conversation:', id);
        }
        
        function loadMoreConversations() {
            console.log('Load more conversations');
        }
        
        function updateLoadMoreButton() {
            // Mock implementation
        }
        
        function showLoadingIndicator() {
            document.getElementById('test-status').textContent = 'Loading...';
        }
        
        function hideLoadingIndicator() {
            document.getElementById('test-status').textContent = 'Ready';
        }
    </script>
    
    <!-- Include the virtual scrolling implementation from popup.js -->
    <script>
        // Copy the VirtualScrollList class and related functions from popup.js
        // This is a simplified version for testing
    </script>
    
    <script src="test-virtual-scrolling.js"></script>
</body>
</html>
