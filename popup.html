<!DOCTYPE html>
<html>
<head>
  <title>LLMLog</title>
  <meta charset="UTF-8">
  <link rel="stylesheet" href="popup.css">
  <link rel="stylesheet" href="lib/github-dark.css">
</head>
<body>
  <a href="#conversation-list" class="skip-link">Skip to main content</a>
  <div id="app" role="application" aria-label="LLMLog Conversation Manager">
    <!-- Main List View -->
    <div id="list-view" role="main">
      <header class="header">
        <h1 id="app-title">LLMLog</h1>
        <div class="search-bar">
          <label for="search-input" class="sr-only">Search conversations</label>
          <input
            type="search"
            id="search-input"
            placeholder="Search conversations..."
            aria-describedby="search-help"
            role="searchbox"
            aria-expanded="false"
            aria-controls="conversation-list"
            autocomplete="off"
          >
          <div id="search-help" class="sr-only">
            Type to search through your conversation history. Results will appear below.
          </div>
        </div>
      </header>
      <main
        id="conversation-list"
        role="list"
        aria-live="polite"
        aria-label="Conversation list"
        aria-describedby="list-status"
        tabindex="0"
      >
        <!-- Conversation items will be dynamically inserted here -->
        <p class="empty-message" role="status" aria-live="polite">No conversations recorded yet.</p>
      </main>
      <div id="list-status" class="sr-only" aria-live="polite" aria-atomic="true">
        <!-- Status updates for screen readers -->
      </div>
    </div>

    <!-- Detail View -->
    <div id="detail-view" class="hidden" role="main" aria-labelledby="detail-title">
      <header class="header detail-header">
        <button
          id="back-button"
          class="back-button"
          aria-label="Go back to conversation list"
          type="button"
        >&larr; Back</button>
        <h1 id="detail-title">Conversation Detail</h1>
      </header>
      <main id="conversation-detail" role="article" tabindex="0">
        <!-- Conversation detail will be dynamically inserted here -->
      </main>
    </div>
  </div>
  <script src="lib/marked.min.js"></script>
  <script src="lib/highlight.min.js"></script>
  <script src="lib/dompurify.min.js"></script>
  <script type="module" src="modules/csp-reporter.js"></script>
  <script src="popup.js"></script>
</body>
</html>