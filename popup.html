<!DOCTYPE html>
<html>
<head>
  <title>LLMLog</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="lib/tailwind.css">
  <link rel="stylesheet" href="popup.css">
  <link rel="stylesheet" href="lib/github-dark.css">
</head>
<body class="bg-gray-50 text-gray-900 w-full max-w-md mx-auto">
  <a href="#conversation-list" class="skip-link absolute -top-10 left-2 bg-blue-600 text-white px-3 py-2 rounded-md z-50 focus:top-2 transition-all duration-300">Skip to main content</a>
  <div id="app" role="application" aria-label="LLMLog Conversation Manager" class="flex flex-col h-full min-h-screen">
    <!-- Main List View -->
    <div id="list-view" role="main" class="flex flex-col h-full">
      <header class="bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10 shadow-sm">
        <h1 id="app-title" class="text-xl font-bold text-gray-900 text-center mb-3">LLMLog</h1>
        <div class="search-bar">
          <label for="search-input" class="sr-only">Search conversations</label>
          <input
            type="search"
            id="search-input"
            placeholder="Search conversations..."
            aria-describedby="search-help"
            role="searchbox"
            aria-expanded="false"
            aria-controls="conversation-list"
            autocomplete="off"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          >
          <div id="search-help" class="sr-only">
            Type to search through your conversation history. Results will appear below.
          </div>
        </div>
      </header>
      <main
        id="conversation-list"
        role="list"
        aria-live="polite"
        aria-label="Conversation list"
        aria-describedby="list-status"
        tabindex="0"
        class="flex-1 overflow-y-auto p-2 space-y-3"
      >
        <!-- Conversation items will be dynamically inserted here -->
        <div class="empty-message flex flex-col items-center justify-center py-16 text-center" role="status" aria-live="polite">
          <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </div>
          <p class="text-gray-500 text-sm">No conversations recorded yet.</p>
          <p class="text-gray-400 text-xs mt-1">Start chatting with AI assistants to see your conversations here.</p>
        </div>
      </main>
      <div id="list-status" class="sr-only" aria-live="polite" aria-atomic="true">
        <!-- Status updates for screen readers -->
      </div>
    </div>

    <!-- Detail View -->
    <div id="detail-view" class="hidden flex flex-col h-full" role="main" aria-labelledby="detail-title">
      <header class="bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10 shadow-sm flex items-center gap-3">
        <button
          id="back-button"
          class="flex items-center gap-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 px-3 py-2 rounded-lg transition-all duration-200 font-medium"
          aria-label="Go back to conversation list"
          type="button"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Back
        </button>
        <h1 id="detail-title" class="text-lg font-semibold text-gray-900 flex-1">Conversation Detail</h1>
      </header>
      <main id="conversation-detail" role="article" tabindex="0" class="flex-1 overflow-y-auto p-4 bg-white">
        <!-- Conversation detail will be dynamically inserted here -->
      </main>
    </div>
  </div>
  <script src="lib/marked.min.js"></script>
  <script src="lib/highlight.min.js"></script>
  <script src="lib/dompurify.min.js"></script>
  <script type="module" src="modules/csp-reporter.js"></script>
  <script src="popup.js"></script>
</body>
</html>