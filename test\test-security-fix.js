/**
 * Test Script for LLMLog postMessage Security Fix
 * 
 * This script verifies that all postMessage calls now use window.location.origin
 * instead of '*' as targetOrigin, preventing data interception by malicious scripts.
 */

console.log('=== LLMLog Security Fix Test Script ===');

// Test 1: Verify no postMessage calls use '*' as targetOrigin
console.log('\n1. Testing postMessage security...');

// Mock a malicious listener that would intercept messages if '*' was used
let interceptedMessages = [];
const maliciousListener = (event) => {
    if (event.data && event.data.type && event.data.type.startsWith('LLMLOG_')) {
        interceptedMessages.push({
            type: event.data.type,
            origin: event.origin,
            timestamp: new Date().toISOString()
        });
        console.log('🚨 SECURITY ISSUE: Malicious script intercepted message:', event.data.type);
    }
};

// Add the malicious listener
window.addEventListener('message', maliciousListener);

// Test 2: Simulate normal LLMLog operation
console.log('\n2. Simulating LLMLog message flow...');

// Wait a moment for any existing messages
setTimeout(() => {
    // Test if we can still receive legitimate messages
    let legitimateMessages = 0;
    const legitimateListener = (event) => {
        if (event.origin === window.location.origin && 
            event.data && 
            event.data.type && 
            event.data.type.startsWith('LLMLOG_')) {
            legitimateMessages++;
            console.log('✅ Legitimate message received:', event.data.type);
        }
    };
    
    window.addEventListener('message', legitimateListener);
    
    // Simulate sending a test message with correct origin
    window.postMessage({
        type: 'LLMLOG_TEST_MESSAGE',
        payload: { test: 'security fix verification' }
    }, window.location.origin);
    
    // Check results after a short delay
    setTimeout(() => {
        console.log('\n=== Security Test Results ===');
        
        if (interceptedMessages.length === 0) {
            console.log('✅ SECURITY FIX SUCCESSFUL: No messages intercepted by malicious listener');
        } else {
            console.log('🚨 SECURITY ISSUE: Messages were intercepted:', interceptedMessages);
        }
        
        if (legitimateMessages > 0) {
            console.log('✅ FUNCTIONALITY PRESERVED: Legitimate messages still work');
        } else {
            console.log('⚠️ No legitimate messages detected (this may be normal if no LLMLog activity)');
        }
        
        // Clean up listeners
        window.removeEventListener('message', maliciousListener);
        window.removeEventListener('message', legitimateListener);
        
        console.log('\n=== Test Complete ===');
        console.log('Current origin:', window.location.origin);
        console.log('This test verifies that postMessage calls use window.location.origin instead of "*"');
    }, 1000);
}, 500);

// Test 3: Check if LLMLog extension is active
console.log('\n3. Checking LLMLog extension status...');
if (typeof window.llmlogDiagnostic === 'function') {
    console.log('✅ LLMLog extension detected and active');
    const diagnostic = window.llmlogDiagnostic();
    console.log('Extension diagnostic:', diagnostic);
} else {
    console.log('ℹ️ LLMLog extension not detected or not active on this page');
}

// Test 4: Verify current domain compatibility
console.log('\n4. Checking domain compatibility...');
const currentDomain = window.location.hostname;
const supportedDomains = [
    'chat.openai.com',
    'chatgpt.com', 
    'gemini.google.com',
    'claude.ai'
];

if (supportedDomains.includes(currentDomain)) {
    console.log('✅ Current domain is supported by LLMLog:', currentDomain);
} else {
    console.log('ℹ️ Current domain is not a supported AI platform:', currentDomain);
    console.log('Supported domains:', supportedDomains);
}
