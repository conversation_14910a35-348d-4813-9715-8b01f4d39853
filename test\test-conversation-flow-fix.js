/**
 * Conversation Flow Fix Test Script
 * 
 * This script tests that the duplicate detection fix allows legitimate
 * new conversations while still preventing true duplicates.
 * 
 * Usage:
 * 1. Open a Claude conversation page
 * 2. Open browser developer tools (F12)
 * 3. Copy and paste this script into the console
 * 4. Follow the test instructions
 */

console.log('=== LLMLog Conversation Flow Fix Test ===');
console.log('Testing that new conversations are captured while duplicates are prevented...\n');

// Test state tracking
let conversationCount = 0;
let duplicateCount = 0;
let conversationLog = [];
let testStartTime = Date.now();

// Monitor conversation captures
const originalPostMessage = window.postMessage;
window.postMessage = function(message, targetOrigin) {
    if (message.type === 'LLMLOG_CONVERSATION') {
        conversationCount++;
        const payload = message.payload;
        
        console.log(`📨 Conversation Captured #${conversationCount}:`, {
            platform: payload.platform,
            url: payload.url,
            promptPreview: payload.prompt.substring(0, 50) + '...',
            responsePreview: payload.response.substring(0, 50) + '...',
            timestamp: payload.createdAt,
            timeSinceTest: Math.round((Date.now() - testStartTime) / 1000) + 's'
        });
        
        // Check for duplicates in our local log
        const isDuplicate = conversationLog.some(conv => 
            conv.prompt === payload.prompt && 
            conv.response === payload.response && 
            conv.platform === payload.platform
        );
        
        if (isDuplicate) {
            duplicateCount++;
            console.warn(`⚠️ DUPLICATE DETECTED #${duplicateCount}!`, {
                type: 'Same content detected',
                platform: payload.platform
            });
        } else {
            console.log('✅ New unique conversation detected');
        }
        
        conversationLog.push({
            platform: payload.platform,
            url: payload.url,
            prompt: payload.prompt,
            response: payload.response,
            timestamp: payload.createdAt,
            captureNumber: conversationCount,
            testTime: Date.now() - testStartTime
        });
    }
    
    return originalPostMessage.call(this, message, targetOrigin);
};

// Test functions
function testNewConversationFlow() {
    console.log('\n🧪 Testing New Conversation Flow...');
    console.log('💡 Instructions:');
    console.log('1. Type a new prompt in the Claude interface');
    console.log('2. Send the message and wait for response');
    console.log('3. Check if the conversation is automatically captured');
    console.log('4. Try sending another message in the same conversation');
    console.log('5. Verify both messages are captured separately');
    
    console.log('\n⏱️ Monitoring for new conversations...');
    console.log('   (This test will monitor for 60 seconds)');
    
    const initialCount = conversationCount;
    
    setTimeout(() => {
        const newConversations = conversationCount - initialCount;
        console.log(`\n📊 Test Results (60 seconds):`, {
            newConversations: newConversations,
            duplicatesDetected: duplicateCount,
            status: newConversations > 0 ? '✅ PASS - Conversations captured' : '❌ FAIL - No conversations captured'
        });
        
        if (newConversations === 0) {
            console.log('\n❌ No conversations captured! This indicates the fix may be too aggressive.');
            console.log('💡 Try:');
            console.log('   1. Sending a message in Claude');
            console.log('   2. Checking console for error messages');
            console.log('   3. Running testDuplicateDetection() to verify duplicate prevention still works');
        }
    }, 60000);
}

function testDuplicateDetection() {
    console.log('\n🔒 Testing Duplicate Detection...');
    
    // Create a test conversation
    const testConversation = {
        platform: 'Claude',
        prompt: 'Test prompt for duplicate detection - ' + Date.now(),
        response: 'Test response for duplicate detection - ' + Date.now(),
        url: window.location.href,
        createdAt: new Date().toISOString(),
        title: 'Test conversation'
    };
    
    console.log('📤 Sending test conversation...');
    window.postMessage({
        type: 'LLMLOG_CONVERSATION',
        payload: testConversation
    }, window.location.origin);
    
    // Send the same conversation again after 2 seconds
    setTimeout(() => {
        console.log('📤 Sending duplicate conversation...');
        window.postMessage({
            type: 'LLMLOG_CONVERSATION',
            payload: {
                ...testConversation,
                createdAt: new Date().toISOString() // Different timestamp but same content
            }
        }, window.location.origin);
        
        // Check results after 3 seconds
        setTimeout(() => {
            const recentConversations = conversationLog.filter(conv => 
                conv.prompt.includes('Test prompt for duplicate detection')
            );
            
            console.log(`\n📊 Duplicate Test Results:`, {
                testConversationsSent: 2,
                testConversationsCaptured: recentConversations.length,
                duplicatesBlocked: 2 - recentConversations.length,
                status: recentConversations.length === 1 ? '✅ PASS - Duplicate blocked' : '❌ FAIL - Duplicate not blocked'
            });
        }, 3000);
    }, 2000);
}

function testPageReloadScenario() {
    console.log('\n🔄 Testing Page Reload Scenario...');
    console.log('💡 Instructions:');
    console.log('1. Note the current conversation count');
    console.log('2. Reload this page (F5 or Ctrl+R)');
    console.log('3. Run this script again');
    console.log('4. Check if existing conversations are re-captured');
    
    console.log(`\n📊 Current State:`, {
        conversationsCaptured: conversationCount,
        duplicatesDetected: duplicateCount,
        currentUrl: window.location.href,
        testDuration: Math.round((Date.now() - testStartTime) / 1000) + ' seconds'
    });
    
    // Check storage for existing conversations
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({
            namespace: 'database',
            action: 'getAllConversations'
        }, (response) => {
            if (response && response.status === 'success') {
                const conversations = response.data;
                const currentUrlConversations = conversations.filter(conv => 
                    conv.url === window.location.href
                );
                
                console.log(`📊 Storage Analysis:`, {
                    totalStoredConversations: conversations.length,
                    currentUrlConversations: currentUrlConversations.length,
                    recommendation: currentUrlConversations.length > 1 ? 
                        'Multiple conversations for this URL - check for duplicates' :
                        'Storage looks clean'
                });
            }
        });
    }
}

function simulateConversationContinuation() {
    console.log('\n💬 Simulating Conversation Continuation...');
    
    const baseConversation = {
        platform: 'Claude',
        url: window.location.href,
        createdAt: new Date().toISOString()
    };
    
    // Simulate first message
    console.log('📤 Simulating first message...');
    window.postMessage({
        type: 'LLMLOG_CONVERSATION',
        payload: {
            ...baseConversation,
            prompt: 'First message in conversation',
            response: 'First response from Claude',
            title: 'First message in conversation'
        }
    }, window.location.origin);
    
    // Simulate second message in same conversation after 3 seconds
    setTimeout(() => {
        console.log('📤 Simulating second message in same conversation...');
        window.postMessage({
            type: 'LLMLOG_CONVERSATION',
            payload: {
                ...baseConversation,
                prompt: 'Second message in same conversation',
                response: 'Second response from Claude',
                title: 'Second message in same conversation',
                createdAt: new Date().toISOString()
            }
        }, window.location.origin);
        
        // Check results
        setTimeout(() => {
            const simulatedConversations = conversationLog.filter(conv => 
                conv.prompt.includes('message in conversation') ||
                conv.prompt.includes('message in same conversation')
            );
            
            console.log(`\n📊 Conversation Continuation Test Results:`, {
                messagesSent: 2,
                messagesCaptured: simulatedConversations.length,
                status: simulatedConversations.length === 2 ? 
                    '✅ PASS - Both messages captured' : 
                    '❌ FAIL - Messages blocked incorrectly'
            });
            
            if (simulatedConversations.length !== 2) {
                console.log('❌ Issue detected: Conversation continuation is being blocked');
                console.log('💡 This means the duplicate detection is too aggressive');
            }
        }, 2000);
    }, 3000);
}

function getDetailedSummary() {
    console.log('\n📊 Detailed Test Summary:');
    console.log('========================');
    console.log(`📨 Total conversations captured: ${conversationCount}`);
    console.log(`⚠️ Duplicates detected: ${duplicateCount}`);
    console.log(`✅ Unique conversations: ${conversationCount - duplicateCount}`);
    console.log(`⏱️ Test duration: ${Math.round((Date.now() - testStartTime) / 1000)} seconds`);
    console.log(`🌐 Current URL: ${window.location.href}`);
    
    if (conversationLog.length > 0) {
        console.log('\n📋 Conversation Log:');
        conversationLog.forEach((conv, index) => {
            console.log(`   ${index + 1}. [${Math.round(conv.testTime / 1000)}s] ${conv.promptPreview || conv.prompt.substring(0, 30)}...`);
        });
    }
    
    console.log('\n🎯 Test Status:');
    if (conversationCount === 0) {
        console.log('❌ CRITICAL: No conversations captured - duplicate detection may be too aggressive');
    } else if (duplicateCount === 0 && conversationCount > 1) {
        console.log('⚠️ WARNING: Multiple conversations but no duplicates detected - check duplicate detection');
    } else {
        console.log('✅ GOOD: Conversations are being captured and duplicates are being detected');
    }
    
    return {
        totalConversations: conversationCount,
        duplicatesDetected: duplicateCount,
        uniqueConversations: conversationCount - duplicateCount,
        testDuration: Math.round((Date.now() - testStartTime) / 1000),
        conversationLog: conversationLog
    };
}

// Export test functions
window.conversationFlowTest = {
    testNewFlow: testNewConversationFlow,
    testDuplicates: testDuplicateDetection,
    testReload: testPageReloadScenario,
    testContinuation: simulateConversationContinuation,
    summary: getDetailedSummary,
    getLog: () => conversationLog,
    getStats: () => ({
        conversations: conversationCount,
        duplicates: duplicateCount,
        unique: conversationCount - duplicateCount,
        duration: Math.round((Date.now() - testStartTime) / 1000)
    })
};

console.log('\n🚀 Conversation Flow Test Suite Loaded!');
console.log('📝 Available commands:');
console.log('   conversationFlowTest.testNewFlow() - Test new conversation capture');
console.log('   conversationFlowTest.testDuplicates() - Test duplicate detection');
console.log('   conversationFlowTest.testContinuation() - Test conversation continuation');
console.log('   conversationFlowTest.testReload() - Test page reload scenario');
console.log('   conversationFlowTest.summary() - Get detailed summary');

console.log('\n💡 Quick Test Instructions:');
console.log('1. Run conversationFlowTest.testNewFlow() and try sending a message');
console.log('2. Run conversationFlowTest.testContinuation() to test multiple messages');
console.log('3. Run conversationFlowTest.testDuplicates() to verify duplicate prevention');

// Auto-start monitoring
console.log('\n⏳ Auto-starting new conversation monitoring...');
testNewConversationFlow();
