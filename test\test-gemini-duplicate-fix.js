/**
 * Test Script for Gemini Duplicate Conversation Fix
 * 
 * This script helps test and monitor the duplicate conversation prevention
 * for the Gemini platform.
 */

console.log('=== Gemini Duplicate Fix Test Script ===');

// Test 1: Check if we're on Gemini
console.log('\n1. Platform Detection...');
const hostname = window.location.hostname;
if (hostname.includes('gemini.google')) {
    console.log('✅ Running on Gemini platform:', hostname);
} else {
    console.log('❌ Not on Gemini platform. This test is for Gemini only.');
    console.log('Current hostname:', hostname);
}

// Test 2: Monitor conversation messages
console.log('\n2. Setting up conversation monitoring...');
let conversationCount = 0;
const conversationLog = [];

const messageListener = (event) => {
    if (event.source === window && event.data.type === 'LLMLOG_CONVERSATION') {
        conversationCount++;
        const payload = event.data.payload;
        
        console.log(`📨 Conversation ${conversationCount}:`, {
            platform: payload.platform,
            prompt: payload.prompt.substring(0, 50) + '...',
            response: payload.response.substring(0, 50) + '...',
            timestamp: payload.createdAt,
            url: payload.url
        });
        
        // Check for duplicates in our log
        const isDuplicate = conversationLog.some(conv => 
            conv.prompt === payload.prompt && 
            conv.response === payload.response &&
            conv.platform === payload.platform
        );
        
        if (isDuplicate) {
            console.warn('⚠️ DUPLICATE DETECTED in conversation log!');
        } else {
            console.log('✅ New unique conversation');
        }
        
        conversationLog.push({
            prompt: payload.prompt,
            response: payload.response,
            platform: payload.platform,
            timestamp: payload.createdAt
        });
    }
};

window.addEventListener('message', messageListener);

// Test 3: Check interceptor state
console.log('\n3. Checking interceptor state...');
if (typeof window.llmlogDiagnostic === 'function') {
    const diagnostic = window.llmlogDiagnostic();
    console.log('Diagnostic info:', diagnostic);
    
    if (diagnostic.platformModulePath && diagnostic.platformModulePath.includes('gemini')) {
        console.log('✅ Gemini platform module loaded');
    } else {
        console.log('❌ Gemini platform module not loaded');
    }
} else {
    console.log('❌ Diagnostic function not available');
}

// Test 4: Monitor XHR requests to Gemini API
console.log('\n4. Setting up XHR monitoring...');
let xhrCount = 0;
const originalXHROpen = XMLHttpRequest.prototype.open;

XMLHttpRequest.prototype.open = function(method, url, ...args) {
    if (url.includes('StreamGenerate')) {
        xhrCount++;
        console.log(`🌐 XHR ${xhrCount} to Gemini API:`, { method, url: url.substring(url.length - 50) });
    }
    return originalXHROpen.apply(this, [method, url, ...args]);
};

// Test 5: Summary after 30 seconds
setTimeout(() => {
    console.log('\n=== 30 Second Summary ===');
    console.log(`Total conversations captured: ${conversationCount}`);
    console.log(`Total XHR requests to Gemini API: ${xhrCount}`);
    
    if (conversationCount > 1 && xhrCount > 1) {
        console.log('⚠️ Multiple conversations detected. Check for duplicates.');
        
        // Analyze for duplicates
        const duplicates = [];
        for (let i = 0; i < conversationLog.length; i++) {
            for (let j = i + 1; j < conversationLog.length; j++) {
                if (conversationLog[i].prompt === conversationLog[j].prompt &&
                    conversationLog[i].response === conversationLog[j].response) {
                    duplicates.push({ index1: i, index2: j });
                }
            }
        }
        
        if (duplicates.length > 0) {
            console.error('❌ DUPLICATES FOUND:', duplicates);
        } else {
            console.log('✅ No duplicates found in conversation log');
        }
    } else if (conversationCount === 1) {
        console.log('✅ Single conversation captured - no duplicates');
    } else if (conversationCount === 0) {
        console.log('⚠️ No conversations captured. Try sending a message to Gemini.');
    }
    
    // Clean up
    window.removeEventListener('message', messageListener);
    XMLHttpRequest.prototype.open = originalXHROpen;
    
    console.log('\n=== Test Complete ===');
    console.log('If you see duplicates, the fix may need adjustment.');
    console.log('If no duplicates, the fix is working correctly.');
}, 30000);

console.log('⏳ Monitoring for 30 seconds...');
console.log('💡 Try sending a message to Gemini to test the duplicate prevention.');
console.log('💡 You can also run window.llmlogDiagnostic() anytime for status.');
