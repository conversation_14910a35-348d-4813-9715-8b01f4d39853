<!DOCTYPE html>
<html lang="en">
<head>
    <title>Accessibility Test - LLMLog</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../popup.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: #4a90e2;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-info {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .test-popup {
            height: 600px;
            position: relative;
            border: 2px solid #ddd;
        }
        
        .accessibility-checklist {
            padding: 20px;
            background: #f8f9fa;
        }
        
        .checklist-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
        
        .checklist-item.warning {
            border-left-color: #ffc107;
        }
        
        .checklist-item.error {
            border-left-color: #dc3545;
        }
        
        .checklist-icon {
            margin-right: 10px;
            font-weight: bold;
        }
        
        .keyboard-shortcuts {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .shortcut {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        
        .shortcut-key {
            background: #6c757d;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>Accessibility Features Test</h1>
            <p>Testing keyboard navigation, screen reader support, and ARIA implementation</p>
        </div>
        
        <div class="test-info">
            <h2>How to Test</h2>
            <div class="keyboard-shortcuts">
                <h3>Keyboard Shortcuts</h3>
                <div class="shortcut">
                    <span>Navigate conversations</span>
                    <span><span class="shortcut-key">↑</span> <span class="shortcut-key">↓</span></span>
                </div>
                <div class="shortcut">
                    <span>Open conversation</span>
                    <span><span class="shortcut-key">Enter</span> or <span class="shortcut-key">Space</span></span>
                </div>
                <div class="shortcut">
                    <span>Delete conversation</span>
                    <span><span class="shortcut-key">Shift</span> + <span class="shortcut-key">Delete</span></span>
                </div>
                <div class="shortcut">
                    <span>Go to first/last</span>
                    <span><span class="shortcut-key">Home</span> / <span class="shortcut-key">End</span></span>
                </div>
                <div class="shortcut">
                    <span>Focus search</span>
                    <span><span class="shortcut-key">Ctrl</span> + <span class="shortcut-key">F</span></span>
                </div>
                <div class="shortcut">
                    <span>Go back (detail view)</span>
                    <span><span class="shortcut-key">Escape</span></span>
                </div>
            </div>
            
            <p><strong>Instructions:</strong></p>
            <ol>
                <li>Use <strong>Tab</strong> to navigate through focusable elements</li>
                <li>Use <strong>arrow keys</strong> to navigate the conversation list</li>
                <li>Press <strong>Enter</strong> or <strong>Space</strong> to activate items</li>
                <li>Test with a screen reader (NVDA, JAWS, VoiceOver)</li>
                <li>Check that all interactive elements have proper labels</li>
            </ol>
        </div>
        
        <div class="test-popup">
            <!-- Copy the popup structure here -->
            <a href="#conversation-list" class="skip-link">Skip to main content</a>
            <div id="app" role="application" aria-label="LLMLog Conversation Manager">
                <!-- Main List View -->
                <div id="list-view" role="main">
                    <header class="header">
                        <h1 id="app-title">LLMLog</h1>
                        <div class="search-bar">
                            <label for="search-input" class="sr-only">Search conversations</label>
                            <input 
                                type="search" 
                                id="search-input" 
                                placeholder="Search conversations..."
                                aria-describedby="search-help"
                                role="searchbox"
                                aria-expanded="false"
                                aria-controls="conversation-list"
                                autocomplete="off"
                            >
                            <div id="search-help" class="sr-only">
                                Type to search through your conversation history. Results will appear below.
                            </div>
                        </div>
                    </header>
                    <main 
                        id="conversation-list" 
                        role="list" 
                        aria-live="polite" 
                        aria-label="Conversation list"
                        aria-describedby="list-status"
                        tabindex="0"
                    >
                        <!-- Test conversations will be inserted here -->
                    </main>
                    <div id="list-status" class="sr-only" aria-live="polite" aria-atomic="true">
                        <!-- Status updates for screen readers -->
                    </div>
                </div>

                <!-- Detail View -->
                <div id="detail-view" class="hidden" role="main" aria-labelledby="detail-title">
                    <header class="header detail-header">
                        <button 
                            id="back-button" 
                            class="back-button" 
                            aria-label="Go back to conversation list"
                            type="button"
                        >&larr; Back</button>
                        <h1 id="detail-title">Conversation Detail</h1>
                    </header>
                    <main id="conversation-detail" role="article" tabindex="0">
                        <!-- Conversation detail will be dynamically inserted here -->
                    </main>
                </div>
            </div>
        </div>
        
        <div class="accessibility-checklist">
            <h2>Accessibility Checklist</h2>
            <div id="checklist-results">
                <!-- Checklist items will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Include required libraries -->
    <script src="../lib/marked.min.js"></script>
    <script src="../lib/highlight.min.js"></script>
    <script src="../lib/dompurify.min.js"></script>
    
    <script>
        // Mock chrome runtime for testing
        window.chrome = {
            runtime: {
                sendMessage: function(message, callback) {
                    setTimeout(() => {
                        callback({ status: 'success', data: [] });
                    }, 100);
                }
            }
        };
        
        // Helper function to escape HTML
        function escapeHTML(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Mock functions
        function deleteConversation(id) {
            console.log('Delete conversation:', id);
        }
        
        function loadMoreConversations() {
            console.log('Load more conversations');
        }
        
        function updateLoadMoreButton() {}
        function showLoadingIndicator() {}
        function hideLoadingIndicator() {}
        function enhanceCodeBlocks() {}
        
        // Global variables
        let allConversations = [];
        let virtualScrollList = null;
        let hasMorePages = false;
        let currentPage = 1;
        let currentView = 'list';
        let accessibilityManager = null;
    </script>
    
    <script src="test-accessibility.js"></script>
</body>
</html>
