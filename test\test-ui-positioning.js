/**
 * UI Positioning Test Script
 * Tests toast notifications and load more button positioning
 */

// Copy the UIFeedback class from popup.js with the updated positioning
class UIFeedback {
  static showToast(message, type = 'info', duration = 4000) {
    // Remove existing toasts of the same type to prevent duplicates
    const existingToasts = document.querySelectorAll(`.toast.toast-${type}`);
    existingToasts.forEach(toast => toast.remove());
    
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    // Create toast content
    const content = document.createElement('div');
    content.className = 'toast-content';
    content.textContent = message;
    
    // Create close button
    const closeButton = document.createElement('button');
    closeButton.className = 'toast-close';
    closeButton.innerHTML = '×';
    closeButton.setAttribute('aria-label', 'Close notification');
    closeButton.addEventListener('click', () => {
      this.hideToast(toast);
    });
    
    toast.appendChild(content);
    toast.appendChild(closeButton);
    
    // Calculate position based on existing toasts
    const existingToastCount = document.querySelectorAll('.toast').length;
    if (existingToastCount > 0) {
      toast.style.bottom = `${20 + (existingToastCount * 80)}px`;
    }
    
    // Add to DOM
    document.body.appendChild(toast);
    
    // Trigger animation
    setTimeout(() => {
      toast.classList.add('toast-show');
    }, 10);
    
    // Auto-hide after duration
    if (duration > 0) {
      setTimeout(() => {
        this.hideToast(toast);
      }, duration);
    }
    
    return toast;
  }
  
  static hideToast(toast) {
    if (toast && toast.parentNode) {
      toast.classList.add('toast-hide');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.remove();
        }
      }, 300);
    }
  }
  
  static showSuccessMessage(message) {
    this.showToast(message, 'success', 3000);
  }
  
  static showErrorMessage(message) {
    this.showToast(message, 'error', 6000);
  }
  
  static showWarningMessage(message) {
    this.showToast(message, 'warning', 4000);
  }
  
  static showInfoMessage(message) {
    this.showToast(message, 'info', 3000);
  }
}

// Test functions
function testSingleToast() {
  UIFeedback.showInfoMessage('This is a single toast notification positioned at bottom center');
  console.log('Single toast displayed');
}

function testMultipleToasts() {
  UIFeedback.showSuccessMessage('First toast - Success message');
  
  setTimeout(() => {
    UIFeedback.showWarningMessage('Second toast - Warning message');
  }, 500);
  
  setTimeout(() => {
    UIFeedback.showErrorMessage('Third toast - Error message');
  }, 1000);
  
  setTimeout(() => {
    UIFeedback.showInfoMessage('Fourth toast - Info message');
  }, 1500);
  
  console.log('Multiple toasts displayed in sequence');
}

function testLoadedMessage() {
  UIFeedback.showInfoMessage('Loaded 20 conversations');
  console.log('Loaded conversations message displayed');
}

function clearToasts() {
  const toasts = document.querySelectorAll('.toast');
  toasts.forEach(toast => {
    UIFeedback.hideToast(toast);
  });
  console.log('All toasts cleared');
}

function showLoadMoreButton() {
  const mockList = document.getElementById('mock-list');
  
  // Remove existing load more button
  const existingButton = mockList.querySelector('.load-more-button');
  if (existingButton) {
    existingButton.remove();
  }
  
  // Create load more button
  const loadMoreButton = document.createElement('button');
  loadMoreButton.className = 'load-more-button';
  loadMoreButton.innerHTML = '📚 Load More Conversations';
  loadMoreButton.style.cssText = `
    position: sticky;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    margin: 10px auto;
    display: block;
    width: auto;
  `;
  
  loadMoreButton.addEventListener('click', () => {
    UIFeedback.showInfoMessage('Loading more conversations...');
    
    // Simulate loading
    setTimeout(() => {
      UIFeedback.showSuccessMessage('Loaded 20 more conversations');
    }, 2000);
  });
  
  mockList.appendChild(loadMoreButton);
  console.log('Load more button displayed');
}

function hideLoadMoreButton() {
  const loadMoreButton = document.querySelector('.load-more-button');
  if (loadMoreButton) {
    loadMoreButton.remove();
    console.log('Load more button hidden');
  }
}

function testLoadMoreInVirtualScroll() {
  const mockList = document.getElementById('mock-list');
  
  // Add virtual scroll class to test the CSS
  mockList.classList.add('virtual-scroll-viewport');
  
  // Remove existing button
  const existingButton = mockList.querySelector('.load-more-button');
  if (existingButton) {
    existingButton.remove();
  }
  
  // Create virtual scroll load more button
  const loadMoreButton = document.createElement('button');
  loadMoreButton.className = 'load-more-button';
  loadMoreButton.innerHTML = '📚 Load More Conversations (Virtual Scroll)';
  
  loadMoreButton.addEventListener('click', () => {
    UIFeedback.showInfoMessage('Virtual scroll loading more...');
    
    // Simulate loading
    setTimeout(() => {
      UIFeedback.showSuccessMessage('Virtual scroll loaded 20 more conversations');
    }, 2000);
  });
  
  mockList.appendChild(loadMoreButton);
  console.log('Virtual scroll load more button displayed');
}

// Utility functions for testing
function logPosition(element, name) {
  const rect = element.getBoundingClientRect();
  console.log(`${name} position:`, {
    top: rect.top,
    left: rect.left,
    bottom: rect.bottom,
    right: rect.right,
    width: rect.width,
    height: rect.height,
    centerX: rect.left + rect.width / 2,
    centerY: rect.top + rect.height / 2
  });
}

function checkCentering() {
  const toasts = document.querySelectorAll('.toast');
  const loadMoreButtons = document.querySelectorAll('.load-more-button');
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;
  
  console.log('Window dimensions:', { width: windowWidth, height: windowHeight });
  
  toasts.forEach((toast, index) => {
    logPosition(toast, `Toast ${index + 1}`);
    
    const rect = toast.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const isHorizontallyCentered = Math.abs(centerX - windowWidth / 2) < 5; // 5px tolerance
    
    console.log(`Toast ${index + 1} horizontally centered:`, isHorizontallyCentered);
  });
  
  loadMoreButtons.forEach((button, index) => {
    logPosition(button, `Load More Button ${index + 1}`);
    
    const rect = button.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const containerRect = button.parentElement.getBoundingClientRect();
    const containerCenterX = containerRect.left + containerRect.width / 2;
    const isHorizontallyCentered = Math.abs(centerX - containerCenterX) < 5; // 5px tolerance
    
    console.log(`Load More Button ${index + 1} horizontally centered:`, isHorizontallyCentered);
  });
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
  console.log('UI positioning test page loaded');
  
  // Add keyboard shortcuts for testing
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey) {
      switch (e.key) {
        case '1':
          e.preventDefault();
          testSingleToast();
          break;
        case '2':
          e.preventDefault();
          testMultipleToasts();
          break;
        case '3':
          e.preventDefault();
          testLoadedMessage();
          break;
        case '4':
          e.preventDefault();
          showLoadMoreButton();
          break;
        case 'c':
          e.preventDefault();
          clearToasts();
          break;
        case 'p':
          e.preventDefault();
          checkCentering();
          break;
      }
    }
  });
  
  // Auto-test after a short delay
  setTimeout(() => {
    console.log('Running automatic positioning test...');
    testLoadedMessage();
    
    setTimeout(() => {
      showLoadMoreButton();
      
      setTimeout(() => {
        checkCentering();
      }, 500);
    }, 1000);
  }, 1000);
  
  console.log('Keyboard shortcuts:');
  console.log('Ctrl+1: Single toast');
  console.log('Ctrl+2: Multiple toasts');
  console.log('Ctrl+3: Loaded message');
  console.log('Ctrl+4: Show load more button');
  console.log('Ctrl+C: Clear toasts');
  console.log('Ctrl+P: Check positioning');
});
