/**
 * Accessibility Test Script
 * Tests keyboard navigation, ARIA implementation, and screen reader support
 */

// Copy the AccessibilityManager class from popup.js
class AccessibilityManager {
  constructor() {
    this.currentFocusIndex = -1;
    this.focusableItems = [];
    this.init();
  }
  
  init() {
    // Add keyboard event listeners
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
    
    // Update focusable items when conversations are rendered
    this.updateFocusableItems();
  }
  
  handleKeyDown(event) {
    const { key, ctrlKey, metaKey } = event;
    
    // Handle different views
    if (currentView === 'list') {
      this.handleListViewKeyDown(event);
    } else if (currentView === 'detail') {
      this.handleDetailViewKeyDown(event);
    }
    
    // Global shortcuts
    if (key === 'Escape') {
      if (currentView === 'detail') {
        event.preventDefault();
        this.goBackToList();
      }
    }
    
    // Search shortcut
    if ((ctrlKey || metaKey) && key === 'f') {
      event.preventDefault();
      this.focusSearch();
    }
  }
  
  handleListViewKeyDown(event) {
    const { key, shiftKey } = event;
    
    switch (key) {
      case 'ArrowDown':
        event.preventDefault();
        this.navigateList(1);
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        this.navigateList(-1);
        break;
        
      case 'Enter':
      case ' ':
        event.preventDefault();
        this.activateCurrentItem();
        break;
        
      case 'Delete':
      case 'Backspace':
        if (shiftKey) {
          event.preventDefault();
          this.deleteCurrentItem();
        }
        break;
        
      case 'Home':
        event.preventDefault();
        this.navigateToFirst();
        break;
        
      case 'End':
        event.preventDefault();
        this.navigateToLast();
        break;
    }
  }
  
  handleDetailViewKeyDown(event) {
    const { key } = event;
    
    switch (key) {
      case 'Escape':
        event.preventDefault();
        this.goBackToList();
        break;
    }
  }
  
  navigateList(direction) {
    this.updateFocusableItems();
    
    if (this.focusableItems.length === 0) return;
    
    this.currentFocusIndex += direction;
    
    // Wrap around
    if (this.currentFocusIndex >= this.focusableItems.length) {
      this.currentFocusIndex = 0;
    } else if (this.currentFocusIndex < 0) {
      this.currentFocusIndex = this.focusableItems.length - 1;
    }
    
    this.focusCurrentItem();
    this.announceCurrentItem();
  }
  
  navigateToFirst() {
    this.updateFocusableItems();
    if (this.focusableItems.length > 0) {
      this.currentFocusIndex = 0;
      this.focusCurrentItem();
      this.announceCurrentItem();
    }
  }
  
  navigateToLast() {
    this.updateFocusableItems();
    if (this.focusableItems.length > 0) {
      this.currentFocusIndex = this.focusableItems.length - 1;
      this.focusCurrentItem();
      this.announceCurrentItem();
    }
  }
  
  updateFocusableItems() {
    const listElement = document.getElementById('conversation-list');
    this.focusableItems = Array.from(listElement.querySelectorAll('.conversation-item'));
  }
  
  focusCurrentItem() {
    if (this.currentFocusIndex >= 0 && this.currentFocusIndex < this.focusableItems.length) {
      const item = this.focusableItems[this.currentFocusIndex];
      item.focus();
      
      // Scroll into view if needed
      item.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
    }
  }
  
  activateCurrentItem() {
    if (this.currentFocusIndex >= 0 && this.currentFocusIndex < this.focusableItems.length) {
      const item = this.focusableItems[this.currentFocusIndex];
      item.click();
    }
  }
  
  deleteCurrentItem() {
    if (this.currentFocusIndex >= 0 && this.currentFocusIndex < this.focusableItems.length) {
      const item = this.focusableItems[this.currentFocusIndex];
      const conversationId = item.getAttribute('data-conversation-id');
      
      if (conversationId) {
        const conversation = allConversations.find(conv => conv.id == conversationId);
        if (conversation && confirm(`Are you sure you want to delete this conversation?\n\n"${conversation.prompt.substring(0, 50)}..."`)) {
          deleteConversation(conversationId);
        }
      }
    }
  }
  
  announceCurrentItem() {
    if (this.currentFocusIndex >= 0 && this.currentFocusIndex < this.focusableItems.length) {
      const item = this.focusableItems[this.currentFocusIndex];
      const title = item.querySelector('.item-title')?.textContent || '';
      const platform = item.querySelector('.platform-badge')?.textContent || '';
      const date = item.querySelector('.item-date')?.textContent || '';
      
      const announcement = `${title} from ${platform}, ${date}. ${this.currentFocusIndex + 1} of ${this.focusableItems.length}`;
      this.announceToScreenReader(announcement);
    }
  }
  
  announceToScreenReader(message) {
    const statusElement = document.getElementById('list-status');
    if (statusElement) {
      statusElement.textContent = message;
    }
    
    // Also log to console for testing
    console.log('Screen reader announcement:', message);
  }
  
  focusSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
      searchInput.focus();
      searchInput.select();
    }
  }
  
  goBackToList() {
    showListView();
  }
  
  onConversationsRendered() {
    this.updateFocusableItems();
    
    // Announce the number of conversations
    const count = this.focusableItems.length;
    if (count > 0) {
      this.announceToScreenReader(`${count} conversations loaded. Use arrow keys to navigate.`);
    }
  }
}

// Test functions
function generateTestConversations() {
  const platforms = ['ChatGPT', 'Gemini', 'Claude'];
  const samplePrompts = [
    'Explain quantum computing in simple terms',
    'Write a Python function to sort an array',
    'What are the benefits of renewable energy?',
    'How does machine learning work?',
    'Create a recipe for chocolate chip cookies'
  ];
  
  allConversations = [];
  
  for (let i = 0; i < 5; i++) {
    const platform = platforms[i % platforms.length];
    const prompt = samplePrompts[i % samplePrompts.length];
    
    allConversations.push({
      id: i + 1,
      platform: platform,
      title: `Test Conversation ${i + 1}`,
      prompt: `${prompt} - Item ${i + 1}`,
      response: `This is a sample response for conversation ${i + 1}. It contains some detailed information about the topic.`,
      url: `https://example.com/conversation/${i + 1}`,
      createdAt: new Date(Date.now() - (i * 60000)).toISOString()
    });
  }
  
  renderTestConversations();
}

function renderTestConversations() {
  const listElement = document.getElementById('conversation-list');
  listElement.innerHTML = '';
  
  allConversations.forEach(conversation => {
    const item = document.createElement('div');
    item.className = 'conversation-item';
    item.setAttribute('data-conversation-id', conversation.id);
    
    // Accessibility attributes
    item.setAttribute('role', 'listitem');
    item.setAttribute('tabindex', '0');
    item.setAttribute('aria-label', `Conversation: ${conversation.title} from ${conversation.platform}, ${new Date(conversation.createdAt).toLocaleString()}`);
    item.setAttribute('aria-describedby', `item-preview-${conversation.id}`);
    
    item.innerHTML = `
      <div class="item-header">
        <span class="platform-badge ${conversation.platform.toLowerCase()}" aria-label="Platform: ${conversation.platform}">${conversation.platform}</span>
        <span class="item-title">${escapeHTML(conversation.title)}</span>
        <span class="item-date" aria-label="Date: ${new Date(conversation.createdAt).toLocaleString()}">${new Date(conversation.createdAt).toLocaleString()}</span>
      </div>
      <div class="item-preview" id="item-preview-${conversation.id}">
        <strong>You:</strong> ${escapeHTML(conversation.prompt.substring(0, 100))}...
      </div>
    `;
    
    // Add click and keyboard event handlers
    const handleActivation = () => {
      showDetailView(conversation);
    };
    
    item.addEventListener('click', handleActivation);
    item.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        handleActivation();
      }
    });

    const menuButton = document.createElement('button');
    menuButton.className = 'menu-button';
    menuButton.innerHTML = '...';
    menuButton.setAttribute('aria-label', `More options for conversation: ${conversation.title}`);
    menuButton.setAttribute('aria-haspopup', 'true');
    menuButton.addEventListener('click', (e) => {
      e.stopPropagation();
      if (confirm(`Are you sure you want to delete this conversation?\n\n"${conversation.prompt.substring(0, 50)}..."`)) {
        deleteConversation(conversation.id);
      }
    });

    item.querySelector('.item-header').appendChild(menuButton);
    listElement.appendChild(item);
  });
  
  // Notify accessibility manager
  if (accessibilityManager) {
    accessibilityManager.onConversationsRendered();
  }
}

function showDetailView(conversation) {
  currentView = 'detail';
  document.getElementById('list-view').classList.add('hidden');
  const detailView = document.getElementById('detail-view');
  detailView.classList.remove('hidden');
  
  document.getElementById('detail-title').textContent = conversation.title;
  
  const detailElement = document.getElementById('conversation-detail');
  detailElement.innerHTML = `
    <div class="detail-section">
      <div class="detail-meta">
        <strong>Platform:</strong> ${conversation.platform} | 
        <strong>Date:</strong> ${new Date(conversation.createdAt).toLocaleString()} |
        <strong>URL:</strong> <a href="${conversation.url}" target="_blank" aria-label="Open conversation URL in new tab">${escapeHTML(conversation.url)}</a>
      </div>
    </div>
    <div class="detail-section">
      <h2>Prompt</h2>
      <div class="detail-content" role="region" aria-label="User prompt">${escapeHTML(conversation.prompt)}</div>
    </div>
    <div class="detail-section">
      <h2>Response</h2>
      <div class="detail-content" role="region" aria-label="AI response">${escapeHTML(conversation.response)}</div>
    </div>
  `;
  
  // Focus management
  setTimeout(() => {
    detailElement.focus();
    
    // Announce to screen reader
    if (accessibilityManager) {
      accessibilityManager.announceToScreenReader(`Viewing conversation: ${conversation.title}. Press Escape to go back to list.`);
    }
  }, 100);
}

function showListView() {
  currentView = 'list';
  document.getElementById('detail-view').classList.add('hidden');
  document.getElementById('list-view').classList.remove('hidden');
  
  // Focus management
  setTimeout(() => {
    const conversationList = document.getElementById('conversation-list');
    conversationList.focus();
    
    // Announce to screen reader
    if (accessibilityManager) {
      accessibilityManager.announceToScreenReader('Returned to conversation list. Use arrow keys to navigate conversations.');
      accessibilityManager.updateFocusableItems();
    }
  }, 100);
}

// Accessibility checklist
function runAccessibilityChecks() {
  const checks = [
    {
      name: 'Skip link present',
      test: () => document.querySelector('.skip-link') !== null,
      type: 'success'
    },
    {
      name: 'ARIA labels on interactive elements',
      test: () => {
        const buttons = document.querySelectorAll('button');
        return Array.from(buttons).every(btn => btn.hasAttribute('aria-label') || btn.textContent.trim());
      },
      type: 'success'
    },
    {
      name: 'Proper heading structure',
      test: () => {
        const h1 = document.querySelectorAll('h1');
        return h1.length >= 1;
      },
      type: 'success'
    },
    {
      name: 'List items have proper roles',
      test: () => {
        const listItems = document.querySelectorAll('.conversation-item');
        return Array.from(listItems).every(item => item.getAttribute('role') === 'listitem');
      },
      type: 'success'
    },
    {
      name: 'Focusable elements have tabindex',
      test: () => {
        const items = document.querySelectorAll('.conversation-item');
        return Array.from(items).every(item => item.hasAttribute('tabindex'));
      },
      type: 'success'
    },
    {
      name: 'Screen reader announcements',
      test: () => document.getElementById('list-status') !== null,
      type: 'success'
    }
  ];
  
  const resultsContainer = document.getElementById('checklist-results');
  resultsContainer.innerHTML = '';
  
  checks.forEach(check => {
    const passed = check.test();
    const item = document.createElement('div');
    item.className = `checklist-item ${passed ? 'success' : 'error'}`;
    
    item.innerHTML = `
      <span class="checklist-icon">${passed ? '✓' : '✗'}</span>
      <span>${check.name}: ${passed ? 'PASS' : 'FAIL'}</span>
    `;
    
    resultsContainer.appendChild(item);
  });
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
  // Initialize accessibility manager
  accessibilityManager = new AccessibilityManager();
  
  // Add back button listener
  document.getElementById('back-button').addEventListener('click', () => {
    showListView();
  });
  
  // Generate test data
  generateTestConversations();
  
  // Run accessibility checks
  setTimeout(runAccessibilityChecks, 500);
  
  console.log('Accessibility test loaded. Use keyboard navigation to test features.');
});
