:root {
  --primary-bg: #f4f6f8;
  --secondary-bg: #ffffff;
  --text-color: #333;
  --border-color: #e0e0e0;
  --header-bg: #fff;
  --search-bg: #f0f0f0;
  --accent-color: #4a90e2;
  --focus-color: #0066cc;
  --focus-outline: 2px solid var(--focus-color);
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus management */
*:focus {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--accent-color);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  width: 450px;
  max-height: 580px;
  margin: 0;
  background-color: var(--primary-bg);
  color: var(--text-color);
  display: flex;
  flex-direction: column;
}

#app {
  display: flex;
  flex-direction: column;
  height: 100%;
}

#detail-view {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.hidden {
  display: none !important;
}

.header {
  padding: 12px 16px;
  background-color: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.detail-header h1 {
  margin: 0;
  font-size: 16px;
  text-align: left;
  flex-grow: 1;
}

.back-button {
  background: none;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  font-size: 16px;
  padding: 5px 10px;
  border-radius: 4px;
}

.back-button:hover {
  background-color: #eef2f7;
}

h1 {
  font-size: 18px;
  margin: 0 0 10px 0;
  text-align: center;
}

.search-bar input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--search-bg);
  box-sizing: border-box;
  font-size: 14px;
}

.search-loading-indicator {
  display: none;
  font-size: 12px;
  color: var(--accent-color);
  text-align: center;
  padding: 4px 0;
  font-style: italic;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

#conversation-list, #conversation-detail {
  flex-grow: 1;
  overflow-y: auto;
  padding: 8px;
}

#conversation-detail {
  padding: 16px;
  background-color: var(--secondary-bg);
  border-radius: 8px;
  margin: 8px;
  border: 1px solid var(--border-color);
}

.empty-message {
  text-align: center;
  color: #888;
  padding: 40px;
}

.loading-indicator {
  text-align: center;
  color: #666;
  padding: 20px;
  font-style: italic;
}

.load-more-button {
  display: block;
  width: calc(100% - 16px);
  padding: 16px 24px;
  margin: 20px 8px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* Load more button - properly centered */
.load-more-button {
  position: sticky;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  max-width: 320px;
  min-width: 240px;
  margin: 20px auto;
  z-index: 100;
  display: block;
  text-align: center;
}

/* Virtual scrolling load more button - centered */
.virtual-scroll-viewport .load-more-button {
  position: sticky !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  max-width: 320px !important;
  min-width: 240px !important;
  margin: 20px auto !important;
  z-index: 100 !important;
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.3) !important;
  display: block !important;
  text-align: center !important;
}

/* End message styling */
.end-message {
  text-align: center;
  padding: 20px;
  color: var(--text-color);
  font-style: italic;
  background: var(--secondary-bg);
  border-radius: 6px;
  margin: 10px auto;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

/* Virtual scrolling end message - centered */
.virtual-scroll-viewport .end-message {
  position: sticky !important;
  bottom: 10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  max-width: 300px !important;
  margin: 10px auto !important;
  z-index: 10 !important;
}

.load-more-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.load-more-button:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
  transform: translateY(-2px);
}

.load-more-button:hover::before {
  left: 100%;
}

.load-more-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.load-more-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.load-more-button:disabled::before {
  display: none;
}

.end-message {
  text-align: center;
  color: #28a745;
  font-size: 14px;
  font-weight: 500;
  padding: 16px;
  margin: 16px 8px;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.1);
}

.conversation-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 16px;
  padding-top: 40px; /* Extra space for date in top-right */
  margin: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.conversation-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4a90e2, #357abd);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.conversation-item:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f0f4f8 100%);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.conversation-item:hover::before {
  opacity: 1;
}

.conversation-item:focus {
  outline: none;
  box-shadow: 0 4px 16px rgba(74, 144, 226, 0.3);
  border-color: var(--accent-color);
}

.conversation-item:focus::before {
  opacity: 1;
}

/* Responsive design for conversation items */
@media (max-width: 480px) {
  .conversation-item {
    margin: 6px 8px;
    padding: 12px;
    padding-top: 36px;
  }

  .item-header {
    flex-wrap: wrap;
    gap: 8px;
    padding-right: 60px;
  }

  .item-title {
    font-size: 14px;
  }

  .platform-badge {
    font-size: 10px;
    padding: 3px 8px;
  }

  .item-date {
    font-size: 10px;
    top: 8px;
    right: 8px;
    padding: 3px 6px;
  }

  .delete-button {
    width: 32px;
    height: 24px;
    font-size: 12px;
    right: 8px;
    bottom: 8px;
  }

  .load-more-button {
    min-width: 200px;
    max-width: 280px;
    font-size: 14px;
    padding: 12px 16px;
  }
}

/* Virtual scrolling styles */
.virtual-scroll-viewport {
  height: 100%;
  overflow-y: auto;
  position: relative;
}

.virtual-scroll-content {
  position: relative;
}

.virtual-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 80px;
  margin-bottom: 0;
}

.virtual-scroll-spacer-top,
.virtual-scroll-spacer-bottom {
  width: 100%;
  pointer-events: none;
}

/* Toast Notifications */
.toast {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(100%);
  min-width: 300px;
  max-width: 400px;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  line-height: 1.4;
  opacity: 0;
  transition: all 0.3s ease;
}

.toast.toast-show {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}

.toast.toast-hide {
  transform: translateX(-50%) translateY(100%);
  opacity: 0;
}

/* Toast stacking for multiple notifications */
.toast:nth-of-type(2) {
  bottom: 100px;
}

.toast:nth-of-type(3) {
  bottom: 180px;
}

.toast:nth-of-type(4) {
  bottom: 260px;
}

.toast-info {
  background: #e3f2fd;
  color: #1565c0;
  border-left: 4px solid #2196f3;
}

.toast-success {
  background: #e8f5e8;
  color: #2e7d32;
  border-left: 4px solid #4caf50;
}

.toast-warning {
  background: #fff3e0;
  color: #ef6c00;
  border-left: 4px solid #ff9800;
}

.toast-error {
  background: #ffebee;
  color: #c62828;
  border-left: 4px solid #f44336;
}

.toast-content {
  flex: 1;
  margin-right: 12px;
}

.toast-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: inherit;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.toast-close:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

/* Loading States */
.loading-state {
  position: relative;
  pointer-events: none;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: var(--text-color);
}

/* Confirm Dialog */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.2s ease;
}

.confirm-dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 90%;
  animation: slideIn 0.3s ease;
}

.dialog-header {
  padding: 20px 20px 0;
  border-bottom: 1px solid #eee;
}

.dialog-header h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: var(--text-color);
}

.dialog-body {
  padding: 20px;
}

.dialog-body p {
  margin: 0;
  color: var(--text-color);
  line-height: 1.5;
}

.dialog-footer {
  padding: 0 20px 20px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.dialog-button {
  padding: 10px 20px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.dialog-button:hover {
  background: #f5f5f5;
}

.dialog-button-confirm {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.dialog-button-confirm:hover {
  background: #357abd;
  border-color: #357abd;
}

.dialog-button:focus {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

body.dialog-open {
  overflow: hidden;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
  position: relative;
  padding-right: 80px; /* Space for delete button */
}

.platform-badge {
  font-size: 11px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 20px;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.platform-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.conversation-item:hover .platform-badge::before {
  left: 100%;
}

.platform-badge.chatgpt {
  background: linear-gradient(135deg, #10a37f 0%, #0d8f6b 100%);
}

.platform-badge.gemini {
  background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
}

.platform-badge.claude {
  background: linear-gradient(135deg, #d97757 0%, #c5654a 100%);
}

.item-title {
  font-weight: 600;
  font-size: 16px;
  color: #2c3e50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
}

.item-date {
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 11px;
  color: #95a5a6;
  white-space: nowrap;
  background: rgba(149, 165, 166, 0.1);
  padding: 4px 8px;
  border-radius: 10px;
  font-weight: 500;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.item-preview {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  background: rgba(74, 144, 226, 0.05);
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 3px solid rgba(74, 144, 226, 0.2);
  margin-top: 4px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

.delete-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(231, 76, 60, 0.2);
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #e74c3c;
  padding: 0;
  border-radius: 8px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 36px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(10px);
  z-index: 2;
}

.conversation-item:hover .delete-button {
  opacity: 1;
  transform: translateY(0);
}

.delete-button:hover {
  background: #e74c3c;
  border-color: #e74c3c;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.delete-button:active {
  transform: scale(0.95);
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h2 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 8px;
  color: #555;
}

.detail-content {
  word-break: break-word;
  line-height: 1.5;
}

.detail-meta {
  font-size: 12px;
  color: #888;
  margin-bottom: 15px;
}
/* --- Markdown Content Styling (Light Theme) --- */
.detail-content {
  line-height: 1.5;
  color: #24292e; /* Dark text for light background */
}

.detail-content h1,
.detail-content h2,
.detail-content h3,
.detail-content h4,
.detail-content h5,
.detail-content h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
  border-bottom: 1px solid #eaecef; /* Lighter border */
  padding-bottom: 0.3em;
}

.detail-content h1 { font-size: 2em; }
.detail-content h2 { font-size: 1.5em; }
.detail-content h3 { font-size: 1.25em; }

.detail-content p {
  margin-bottom: 16px;
}

.detail-content a {
  color: #0366d6; /* Standard link color */
  text-decoration: none;
}

.detail-content a:hover {
  text-decoration: underline;
}

.detail-content ul,
.detail-content ol {
  padding-left: 2em;
  margin-bottom: 16px;
}

.detail-content li {
  margin-bottom: 0.25em;
}

.detail-content blockquote {
  margin: 0 0 16px 0;
  padding: 0 1em;
  color: #6a737d; /* Adjusted for light theme */
  border-left: 0.25em solid #dfe2e5; /* Lighter border */
}

.detail-content code {
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 85%;
  padding: 0.2em 0.4em;
  margin: 0;
  background-color: rgba(27,31,35,0.05); /* Light background for code */
  border-radius: 6px;
}

.detail-content pre {
  position: relative; /* Required for positioning the copy button */
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa; /* Light background for code blocks */
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  margin-bottom: 16px;
}

.detail-content pre code {
  padding: 0;
  margin: 0;
  background-color: transparent;
  border-radius: 0;
  color: inherit; /* Inherit color from pre */
}

/* --- Copy Button Styling --- */
.copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  font-family: inherit;
  cursor: pointer;
  opacity: 0; /* Hidden by default */
  transition: opacity 0.2s ease-in-out, background-color 0.2s;
}

.detail-content pre:hover .copy-button {
  opacity: 1; /* Show on hover */
}

.copy-button:hover {
  background-color: #d0d0d0;
}

.copy-button:active {
    background-color: #c0c0c0;
}