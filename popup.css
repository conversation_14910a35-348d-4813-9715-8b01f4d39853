/* Custom CSS Variables for Tailwind Integration */
:root {
  --primary-bg: #f9fafb;
  --secondary-bg: #ffffff;
  --text-color: #111827;
  --border-color: #e5e7eb;
  --header-bg: #fff;
  --search-bg: #f9fafb;
  --accent-color: #3b82f6;
  --focus-color: #2563eb;
  --focus-outline: 2px solid var(--focus-color);
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Enhanced focus management with Tailwind integration */
*:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

/* Skip link enhanced with Tailwind classes */
.skip-link {
  position: absolute;
  top: -2.5rem;
  left: 0.5rem;
  background: var(--accent-color);
  color: white;
  padding: 0.5rem 0.75rem;
  text-decoration: none;
  border-radius: 0.375rem;
  z-index: 1000;
  transition: top 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.skip-link:focus {
  top: 0.5rem;
}

/* Body styling with Tailwind integration */
body {
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  width: 450px;
  max-height: 580px;
  margin: 0;
  background-color: var(--primary-bg);
  color: var(--text-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* App container */
#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 580px;
}

/* Detail view */
#detail-view {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Hidden utility */
.hidden {
  display: none !important;
}

/* Search loading indicator */
.search-loading-indicator {
  display: none;
  font-size: 0.75rem;
  color: var(--accent-color);
  text-align: center;
  padding: 0.25rem 0;
  font-style: italic;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* Empty message and loading states */
.empty-message {
  text-align: center;
  color: #6b7280;
  padding: 2.5rem;
}

.loading-indicator {
  text-align: center;
  color: #6b7280;
  padding: 1.25rem;
  font-style: italic;
  font-size: 0.875rem;
}

/* Load More Button - Modern Card Design */
.load-more-button {
  display: block;
  width: auto;
  max-width: 20rem;
  min-width: 15rem;
  padding: 1rem 1.5rem;
  margin: 1.25rem auto;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 0.75rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  position: sticky;
  bottom: 1.25rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  text-align: center;
  overflow: hidden;
}

/* Virtual scrolling load more button */
.virtual-scroll-viewport .load-more-button {
  position: sticky !important;
  bottom: 1.25rem !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  max-width: 20rem !important;
  min-width: 15rem !important;
  margin: 1.25rem auto !important;
  z-index: 100 !important;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3) !important;
  display: block !important;
  text-align: center !important;
}

/* End message styling */
.end-message {
  text-align: center;
  padding: 1.25rem;
  color: var(--text-color);
  font-style: italic;
  background: var(--secondary-bg);
  border-radius: 0.5rem;
  margin: 0.625rem auto;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  max-width: 18.75rem;
}

/* Virtual scrolling end message - centered */
.virtual-scroll-viewport .end-message {
  position: sticky !important;
  bottom: 0.625rem !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  max-width: 18.75rem !important;
  margin: 0.625rem auto !important;
  z-index: 10 !important;
}

/* Load More Button Effects */
.load-more-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.load-more-button:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  transform: translateX(-50%) translateY(-2px);
}

.load-more-button:hover::before {
  left: 100%;
}

.load-more-button:active {
  transform: translateX(-50%) translateY(0);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.load-more-button:disabled {
  background: #6b7280;
  cursor: not-allowed;
  box-shadow: none;
  transform: translateX(-50%);
}

.load-more-button:disabled::before {
  display: none;
}

/* Success End Message */
.end-message.success {
  text-align: center;
  color: #059669;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 1rem;
  margin: 1rem 0.5rem;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border: 1px solid #a7f3d0;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.1);
  max-width: 20rem;
  margin-left: auto;
  margin-right: auto;
}

/* Modern Conversation Card */
.conversation-item {
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1rem;
  padding-top: 2.5rem; /* Extra space for date in top-right */
  margin: 0.5rem 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

/* Conversation Card Effects */
.conversation-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #2563eb);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.conversation-item:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  border-color: #dbeafe;
}

.conversation-item:hover::before {
  opacity: 1;
}

.conversation-item:focus {
  outline: none;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  border-color: #3b82f6;
}

.conversation-item:focus::before {
  opacity: 1;
}

/* Responsive design for conversation items */
@media (max-width: 480px) {
  .conversation-item {
    margin: 0.375rem 0.5rem;
    padding: 0.75rem;
    padding-top: 2.25rem;
  }

  .item-header {
    flex-wrap: wrap;
    gap: 0.5rem;
    padding-right: 3.75rem;
  }

  .item-title {
    font-size: 0.875rem;
  }

  .platform-badge {
    font-size: 0.625rem;
    padding: 0.1875rem 0.5rem;
  }

  .item-date {
    font-size: 0.625rem;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.1875rem 0.375rem;
  }

  .delete-button {
    width: 2rem;
    height: 1.5rem;
    font-size: 0.75rem;
    right: 0.5rem;
    bottom: 0.5rem;
  }

  .load-more-button {
    min-width: 12.5rem;
    max-width: 17.5rem;
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
  }
}

/* Virtual scrolling styles */
.virtual-scroll-viewport {
  height: 100%;
  overflow-y: auto;
  position: relative;
}

.virtual-scroll-content {
  position: relative;
}

.virtual-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 5rem;
  margin-bottom: 0;
}

.virtual-scroll-spacer-top,
.virtual-scroll-spacer-bottom {
  width: 100%;
  pointer-events: none;
}

/* Modern Toast Notifications */
.toast {
  position: fixed;
  bottom: 1.25rem;
  left: 50%;
  transform: translateX(-50%) translateY(100%);
  min-width: 18.75rem;
  max-width: 25rem;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
  line-height: 1.4;
  opacity: 0;
  transition: all 0.3s ease;
}

.toast.toast-show {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}

.toast.toast-hide {
  transform: translateX(-50%) translateY(100%);
  opacity: 0;
}

/* Toast stacking for multiple notifications */
.toast:nth-of-type(2) {
  bottom: 6.25rem;
}

.toast:nth-of-type(3) {
  bottom: 11.25rem;
}

.toast:nth-of-type(4) {
  bottom: 16.25rem;
}

/* Toast Color Variants */
.toast-info {
  background: #dbeafe;
  color: #1e40af;
  border-left: 4px solid #3b82f6;
}

.toast-success {
  background: #d1fae5;
  color: #065f46;
  border-left: 4px solid #10b981;
}

.toast-warning {
  background: #fef3c7;
  color: #92400e;
  border-left: 4px solid #f59e0b;
}

.toast-error {
  background: #fee2e2;
  color: #991b1b;
  border-left: 4px solid #ef4444;
}

.toast-content {
  flex: 1;
  margin-right: 12px;
}

.toast-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: inherit;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.toast-close:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

/* Loading States */
.loading-state {
  position: relative;
  pointer-events: none;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: var(--text-color);
}

/* Confirm Dialog */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.2s ease;
}

.confirm-dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 90%;
  animation: slideIn 0.3s ease;
}

.dialog-header {
  padding: 20px 20px 0;
  border-bottom: 1px solid #eee;
}

.dialog-header h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: var(--text-color);
}

.dialog-body {
  padding: 20px;
}

.dialog-body p {
  margin: 0;
  color: var(--text-color);
  line-height: 1.5;
}

.dialog-footer {
  padding: 0 20px 20px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.dialog-button {
  padding: 10px 20px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.dialog-button:hover {
  background: #f5f5f5;
}

.dialog-button-confirm {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.dialog-button-confirm:hover {
  background: #357abd;
  border-color: #357abd;
}

.dialog-button:focus {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

body.dialog-open {
  overflow: hidden;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Card Item Header */
.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  gap: 0.75rem;
  position: relative;
  padding-right: 5rem; /* Space for delete button */
}

/* Platform Badge */
.platform-badge {
  font-size: 0.6875rem;
  font-weight: 600;
  padding: 0.25rem 0.625rem;
  border-radius: 1.25rem;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.platform-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.conversation-item:hover .platform-badge::before {
  left: 100%;
}

/* Platform-specific colors */
.platform-badge.chatgpt {
  background: linear-gradient(135deg, #10a37f 0%, #0d8f6b 100%);
}

.platform-badge.gemini {
  background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
}

.platform-badge.claude {
  background: linear-gradient(135deg, #d97757 0%, #c5654a 100%);
}

/* Item Title */
.item-title {
  font-weight: 600;
  font-size: 1rem;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
}

/* Item Date - Top Right Corner */
.item-date {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  font-size: 0.6875rem;
  color: #6b7280;
  white-space: nowrap;
  background: rgba(107, 114, 128, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 0.625rem;
  font-weight: 500;
  z-index: 1;
  backdrop-filter: blur(10px);
}

/* Item Preview */
.item-preview {
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
  background: rgba(59, 130, 246, 0.05);
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  border-left: 3px solid rgba(59, 130, 246, 0.2);
  margin-top: 0.25rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

/* Delete Button - Integrated Design */
.delete-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(239, 68, 68, 0.2);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  color: #ef4444;
  padding: 0;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: absolute;
  right: 0.75rem;
  bottom: 0.75rem;
  width: 2.25rem;
  height: 1.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(0.625rem);
  z-index: 2;
}

.conversation-item:hover .delete-button {
  opacity: 1;
  transform: translateY(0);
}

.delete-button:hover {
  background: #ef4444;
  border-color: #ef4444;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.delete-button:active {
  transform: scale(0.95);
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h2 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 8px;
  color: #555;
}

.detail-content {
  word-break: break-word;
  line-height: 1.5;
}

.detail-meta {
  font-size: 12px;
  color: #888;
  margin-bottom: 15px;
}
/* --- Markdown Content Styling (Light Theme) --- */
.detail-content {
  line-height: 1.5;
  color: #24292e; /* Dark text for light background */
}

.detail-content h1,
.detail-content h2,
.detail-content h3,
.detail-content h4,
.detail-content h5,
.detail-content h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
  border-bottom: 1px solid #eaecef; /* Lighter border */
  padding-bottom: 0.3em;
}

.detail-content h1 { font-size: 2em; }
.detail-content h2 { font-size: 1.5em; }
.detail-content h3 { font-size: 1.25em; }

.detail-content p {
  margin-bottom: 16px;
}

.detail-content a {
  color: #0366d6; /* Standard link color */
  text-decoration: none;
}

.detail-content a:hover {
  text-decoration: underline;
}

.detail-content ul,
.detail-content ol {
  padding-left: 2em;
  margin-bottom: 16px;
}

.detail-content li {
  margin-bottom: 0.25em;
}

.detail-content blockquote {
  margin: 0 0 16px 0;
  padding: 0 1em;
  color: #6a737d; /* Adjusted for light theme */
  border-left: 0.25em solid #dfe2e5; /* Lighter border */
}

.detail-content code {
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 85%;
  padding: 0.2em 0.4em;
  margin: 0;
  background-color: rgba(27,31,35,0.05); /* Light background for code */
  border-radius: 6px;
}

.detail-content pre {
  position: relative; /* Required for positioning the copy button */
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa; /* Light background for code blocks */
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  margin-bottom: 16px;
}

.detail-content pre code {
  padding: 0;
  margin: 0;
  background-color: transparent;
  border-radius: 0;
  color: inherit; /* Inherit color from pre */
}

/* --- Copy Button Styling --- */
.copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  font-family: inherit;
  cursor: pointer;
  opacity: 0; /* Hidden by default */
  transition: opacity 0.2s ease-in-out, background-color 0.2s;
}

.detail-content pre:hover .copy-button {
  opacity: 1; /* Show on hover */
}

.copy-button:hover {
  background-color: #d0d0d0;
}

.copy-button:active {
    background-color: #c0c0c0;
}