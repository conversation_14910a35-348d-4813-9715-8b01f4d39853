<!DOCTYPE html>
<html>
<head>
    <title>DOMPurify Sanitization Test</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .input {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .output {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            border: 1px solid #4CAF50;
        }
        .dangerous {
            background: #ffe8e8;
            border: 1px solid #f44336;
        }
        .safe {
            background: #e8f5e8;
            border: 1px solid #4CAF50;
        }
        .result {
            font-weight: bold;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .pass {
            background: #4CAF50;
            color: white;
        }
        .fail {
            background: #f44336;
            color: white;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>DOMPurify Sanitization Test Suite</h1>
    <p>This test suite verifies that DOMPurify properly sanitizes malicious content while preserving legitimate markdown.</p>
    
    <div id="test-results"></div>

    <!-- Include the same libraries as popup.html -->
    <script src="lib/marked.min.js"></script>
    <script src="lib/dompurify.min.js"></script>

    <script>
        // Test cases for DOMPurify sanitization
        const testCases = [
            {
                name: "Basic Markdown - Headers",
                input: "# Header 1\n## Header 2\n### Header 3",
                expectSafe: true,
                description: "Should preserve basic markdown headers"
            },
            {
                name: "Basic Markdown - Lists",
                input: "- Item 1\n- Item 2\n- Item 3\n\n1. Numbered item\n2. Another item",
                expectSafe: true,
                description: "Should preserve markdown lists"
            },
            {
                name: "Basic Markdown - Code Blocks",
                input: "```javascript\nfunction hello() {\n    console.log('Hello World');\n}\n```",
                expectSafe: true,
                description: "Should preserve code blocks"
            },
            {
                name: "Basic Markdown - Links",
                input: "[Google](https://google.com)",
                expectSafe: true,
                description: "Should preserve legitimate links"
            },
            {
                name: "XSS Attack - Script Tag",
                input: "<script>alert('XSS Attack!');</script>",
                expectSafe: false,
                description: "Should remove script tags"
            },
            {
                name: "XSS Attack - Image with onerror",
                input: '<img src="x" onerror="alert(\'XSS via image onerror\')">',
                expectSafe: false,
                description: "Should remove onerror handlers"
            },
            {
                name: "XSS Attack - Link with javascript:",
                input: '<a href="javascript:alert(\'XSS via javascript link\')">Click me</a>',
                expectSafe: false,
                description: "Should remove javascript: URLs"
            },
            {
                name: "XSS Attack - Iframe",
                input: '<iframe src="javascript:alert(\'XSS via iframe\')"></iframe>',
                expectSafe: false,
                description: "Should remove iframe tags"
            },
            {
                name: "XSS Attack - SVG with script",
                input: '<svg><script>alert("XSS via SVG")</script></svg>',
                expectSafe: false,
                description: "Should remove scripts in SVG"
            },
            {
                name: "XSS Attack - Event handlers",
                input: '<div onclick="alert(\'XSS via onclick\')">Click me</div>',
                expectSafe: false,
                description: "Should remove event handlers"
            },
            {
                name: "Markdown with HTML - Safe",
                input: "# Title\n\nThis is **bold** and *italic* text.\n\n<strong>This should be safe</strong>",
                expectSafe: true,
                description: "Should preserve safe HTML in markdown"
            },
            {
                name: "Complex Markdown",
                input: "# AI Response\n\nHere's a code example:\n\n```python\ndef hello():\n    print('Hello!')\n```\n\n**Important:** This is safe content.\n\n- Point 1\n- Point 2",
                expectSafe: true,
                description: "Should preserve complex legitimate markdown"
            }
        ];

        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            let passCount = 0;
            let totalTests = testCases.length;

            testCases.forEach((testCase, index) => {
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';

                // Parse markdown and sanitize
                const markedOutput = marked.parse(testCase.input);
                const sanitizedOutput = DOMPurify.sanitize(markedOutput);

                // Check if dangerous content was removed
                const containsScript = sanitizedOutput.includes('<script') || 
                                     sanitizedOutput.includes('javascript:') ||
                                     sanitizedOutput.includes('onerror=') ||
                                     sanitizedOutput.includes('onclick=') ||
                                     sanitizedOutput.includes('<iframe');

                const testPassed = testCase.expectSafe ? !containsScript : containsScript !== sanitizedOutput.includes('script');

                if (testPassed) passCount++;

                testDiv.innerHTML = `
                    <h3>Test ${index + 1}: ${testCase.name}</h3>
                    <p><strong>Description:</strong> ${testCase.description}</p>
                    <p><strong>Expected:</strong> ${testCase.expectSafe ? 'Safe content preserved' : 'Dangerous content removed'}</p>
                    
                    <div class="input">
                        <strong>Input Markdown:</strong><br>
                        ${escapeHtml(testCase.input)}
                    </div>
                    
                    <div class="output">
                        <strong>Marked.js Output (before sanitization):</strong><br>
                        <pre>${escapeHtml(markedOutput)}</pre>
                    </div>
                    
                    <div class="output">
                        <strong>DOMPurify Sanitized Output:</strong><br>
                        <pre>${escapeHtml(sanitizedOutput)}</pre>
                    </div>
                    
                    <div class="output">
                        <strong>Rendered Result:</strong><br>
                        <div style="border: 1px solid #ccc; padding: 10px; background: white;">
                            ${sanitizedOutput}
                        </div>
                    </div>
                    
                    <div class="result ${testPassed ? 'pass' : 'fail'}">
                        ${testPassed ? 'PASS' : 'FAIL'} - ${testPassed ? 'Test passed successfully' : 'Test failed'}
                    </div>
                `;

                resultsContainer.appendChild(testDiv);
            });

            // Add summary
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'test-case';
            summaryDiv.innerHTML = `
                <h2>Test Summary</h2>
                <div class="result ${passCount === totalTests ? 'pass' : 'fail'}">
                    ${passCount}/${totalTests} tests passed (${Math.round(passCount/totalTests*100)}%)
                </div>
                <p><strong>Status:</strong> ${passCount === totalTests ? 'All tests passed! DOMPurify is working correctly.' : 'Some tests failed. Please review the implementation.'}</p>
            `;
            resultsContainer.insertBefore(summaryDiv, resultsContainer.firstChild);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
